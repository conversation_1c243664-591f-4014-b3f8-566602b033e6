# 应用配置面板
VITE_APP_SETTING = false
# 页面标题
VITE_APP_TITLE = 网站运行管理
# 接口请求地址，会设置到 axios 的 baseURL 参数上
# 国网客户地址，国网走了nginx转发
# 开发人员个人虚拟机
# VITE_APP_API_BASEURL = http://192.168.174.140:8095/api/sgcc/
# 客户电脑虚拟机
# VITE_APP_API_BASEURL = http://192.168.163.130:8095/api/sgcc/
# 客户生产环境
# VITE_APP_API_BASEURL = http://***********:28080/api/sgcc/
# 中讯测试环境
VITE_APP_API_BASEURL = http://**************:8202/api/sgcc/
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =
# 是否禁用开发者工具，可防止被调试
VITE_APP_DISABLE_DEVTOOL = false

# 是否在打包时启用 Mock
VITE_BUILD_MOCK = false
# 是否在打包时生成 sourcemap
VITE_BUILD_SOURCEMAP = false
# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS =
# 是否在打包后生成存档，支持 zip 和 tar
VITE_BUILD_ARCHIVE =

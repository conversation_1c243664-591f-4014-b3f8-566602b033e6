{"app": {"profile": "Profile", "preferences": "Preferences", "hotkeys": "Hotkeys Introduction", "logout": "Logout", "search": {"text": "Search", "type": {"menu": "Search menus", "tab": "Search tabs"}, "input": "Support title, URL fuzzy query", "empty": "Not found", "up_down": "Choice", "enter": "Enter", "esc": "Exit"}, "tabbar": {"reload": "Reload", "close": "Close", "pin": "<PERSON>n", "unpin": "Unpin", "maximize": "Maximize", "newWindow": "Open In New Window", "searchTabs": "Search Tabs", "closeOtherSide": "Close Otherside Tabs", "closeLeftSide": "Close Leftside Tabs", "closeRightSide": "Close Rightside Tabs"}}, "route": {"undefined": "[ No title ]", "home": "Home", "login": "<PERSON><PERSON>", "ssologin": "SSO Login", "personal": {"notification": "Notification"}, "user": {"root": "User", "usermanage": "User management", "rolemanage": "Role management", "roleandusermanage": "Role and user management", "usergroupmanage": "User group management"}, "checkconfig": {"root": "Web management", "websiteColumnManagement": "Website column management", "validityRuleConfig": "Validity rules configuration", "websiteDetail": "Website detail"}, "checkresult": {"root": "Check result", "resultview": "Check result view"}, "external": {"root": "External website", "effective": "effective rule enforcement", "website": "External link"}, "log": {"root": "Log", "manage": "Log management"}, "reportquery": {"root": "Report query", "historyreportquery": "History report query"}, "temporarytask": {"root": "Temporary task", "tasklist": "Temporary task list"}, "config": {"root": "Configuration management", "unitmanage": "Unit management"}}}
// vite.config.ts
import fs2 from "node:fs";
import path2 from "node:path";
import process2 from "node:process";
import { defineConfig, loadEnv } from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/vite@5.4.3_@types+node@22.5.4_sass@1.78.0_terser@5.32.0/node_modules/vite/dist/node/index.js";
import dayjs2 from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js";

// package.json
var package_default = {
  type: "module",
  version: "4.11.0",
  engines: {
    node: "^18.18.0 || ^20.9.0 || >=21.1.0"
  },
  scripts: {
    dev: "vite",
    build: "vue-tsc && vite build",
    "build:test": "vue-tsc && vite build --mode test",
    serve: "http-server ./sgcc_front -o",
    "serve:test": "http-server ./sgcc_front-test -o",
    svgo: "svgo -f src/assets/icons",
    new: "plop",
    "generate:icons": "esno ./scripts/generate.icons.ts",
    lint: "npm-run-all -s lint:tsc lint:eslint lint:stylelint",
    "lint:tsc": "vue-tsc",
    "lint:eslint": "eslint . --cache --fix",
    "lint:stylelint": 'stylelint "src/**/*.{css,scss,vue}" --cache --fix',
    postinstall: "simple-git-hooks",
    preinstall: "npx only-allow pnpm",
    commit: "git cz",
    release: "bumpp"
  },
  dependencies: {
    "@headlessui/vue": "^1.7.22",
    "@imengyu/vue3-context-menu": "^1.4.2",
    "@types/crypto-js": "^4.2.2",
    "@vueuse/components": "^11.0.3",
    "@vueuse/core": "^11.0.3",
    "@vueuse/integrations": "^11.0.3",
    axios: "^1.7.7",
    "crypto-js": "^4.2.0",
    dayjs: "^1.11.13",
    defu: "^6.1.4",
    "disable-devtool": "^0.3.7",
    "docx-preview": "^0.3.2",
    "element-plus": "^2.8.1",
    eruda: "^3.2.3",
    "floating-vue": "5.2.2",
    "hotkeys-js": "^3.13.7",
    "lodash-es": "^4.17.21",
    "medium-zoom": "^1.1.0",
    mitt: "^3.0.1",
    mockjs: "^1.1.0",
    nprogress: "^0.2.0",
    overlayscrollbars: "^2.10.0",
    "overlayscrollbars-vue": "^0.5.9",
    "path-browserify": "^1.0.1",
    "path-to-regexp": "^7.1.0",
    pinia: "^2.2.2",
    "pinyin-pro": "^3.24.2",
    qs: "^6.13.0",
    scule: "^1.3.0",
    sortablejs: "^1.15.2",
    spinkit: "^2.0.1",
    "timeago.js": "^4.0.2",
    "ts-md5": "^1.3.1",
    "v-wave": "^2.0.0",
    vconsole: "^3.15.1",
    vue: "^3.4.38",
    "vue-i18n": "^9.14.0",
    "vue-m-message": "^4.0.2",
    "vue-router": "^4.4.3",
    "vxe-table": "^4.7.80",
    "watermark-js-plus": "^1.5.5"
  },
  devDependencies: {
    "@antfu/eslint-config": "2.24.1",
    "@iconify/json": "^2.2.243",
    "@iconify/vue": "^4.1.2",
    "@intlify/unplugin-vue-i18n": "^4.0.0",
    "@stylistic/stylelint-config": "^2.0.0",
    "@types/lodash-es": "^4.17.12",
    "@types/mockjs": "^1.0.10",
    "@types/nprogress": "^0.2.3",
    "@types/path-browserify": "^1.0.3",
    "@types/qs": "^6.9.15",
    "@types/sortablejs": "^1.15.8",
    "@unocss/eslint-plugin": "^0.62.3",
    "@vitejs/plugin-legacy": "^5.4.2",
    "@vitejs/plugin-vue": "^5.1.3",
    "@vitejs/plugin-vue-jsx": "^4.0.1",
    archiver: "^7.0.1",
    autoprefixer: "^10.4.20",
    boxen: "^8.0.1",
    bumpp: "^9.5.2",
    "cz-git": "^1.9.4",
    eslint: "^9.9.1",
    esno: "^4.7.0",
    "fs-extra": "^11.2.0",
    "http-server": "^14.1.1",
    inquirer: "^10.1.8",
    "lint-staged": "^15.2.9",
    "npm-run-all2": "^6.2.2",
    picocolors: "^1.0.1",
    plop: "^4.0.1",
    postcss: "^8.4.42",
    "postcss-nested": "^6.2.0",
    sass: "^1.77.8",
    "simple-git-hooks": "^2.11.1",
    stylelint: "^16.9.0",
    "stylelint-config-recess-order": "^5.1.0",
    "stylelint-config-standard-scss": "^13.1.0",
    "stylelint-config-standard-vue": "^1.0.0",
    "stylelint-scss": "^6.5.1",
    svgo: "^3.3.2",
    typescript: "^5.5.4",
    unocss: "^0.62.3",
    "unocss-preset-scrollbar": "^0.3.1",
    "unplugin-auto-import": "^0.18.2",
    "unplugin-turbo-console": "^1.10.1",
    "unplugin-vue-components": "^0.27.4",
    vite: "^5.4.2",
    "vite-plugin-banner": "^0.7.1",
    "vite-plugin-compression2": "^1.2.0",
    "vite-plugin-fake-server": "^2.1.1",
    "vite-plugin-pages": "^0.32.3",
    "vite-plugin-svg-icons": "^2.0.1",
    "vite-plugin-vue-devtools": "^7.3.9",
    "vite-plugin-vue-meta-layouts": "^0.4.3",
    "vue-tsc": "^2.1.4"
  },
  "simple-git-hooks": {
    "pre-commit": "pnpm lint-staged",
    preserveUnused: true
  },
  config: {
    commitizen: {
      path: "node_modules/cz-git"
    }
  }
};

// vite/plugins.ts
import path from "node:path";
import process from "node:process";
import fs from "node:fs";
import dayjs from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js";
import vue from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/@vitejs+plugin-vue@5.1.3_vi_17ccc866f3a4dcc12777c09f98b6c5e7/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0._03e3b7a436086b3acad7ed7337daf11f/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import vueLegacy from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/@vitejs+plugin-legacy@5.4.2_34520957c9ca1e79fd8b5536127cf2d5/node_modules/@vitejs/plugin-legacy/dist/index.mjs";
import VueDevTools from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/vite-plugin-vue-devtools@7._b8123916e1c19ff74e241080cfcabfe9/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import autoImport from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/unplugin-auto-import@0.18.2_358d69370997b84452223c3c91dbad8f/node_modules/unplugin-auto-import/dist/vite.js";
import components from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/unplugin-vue-components@0.2_84ef37e49f792dc0fc576173ceeeff78/node_modules/unplugin-vue-components/dist/vite.js";
import Unocss from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/unocss@0.62.3_postcss@8.4.4_8a595c9467e797e904dd0f284a97ade7/node_modules/unocss/dist/vite.mjs";
import { createSvgIconsPlugin } from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_a26f791cde68a5143333fea744f246b7/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import { vitePluginFakeServer } from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/vite-plugin-fake-server@2.1.2/node_modules/vite-plugin-fake-server/dist/index.mjs";
import vueI18n from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/@intlify+unplugin-vue-i18n@_1c6d697c1f1747054f00f1415ffb7a7a/node_modules/@intlify/unplugin-vue-i18n/lib/vite.mjs";
import Layouts from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/vite-plugin-vue-meta-layout_d58aa5cfcfe36d64b2972516a2f9a283/node_modules/vite-plugin-vue-meta-layouts/dist/index.mjs";
import Pages from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/vite-plugin-pages@0.32.3_@v_2758a963da60be6b543f9a1855429d80/node_modules/vite-plugin-pages/dist/index.js";
import { compression } from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/vite-plugin-compression2@1.3.0_rollup@4.21.2/node_modules/vite-plugin-compression2/dist/index.mjs";
import archiver from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/archiver@7.0.1/node_modules/archiver/index.js";
import TurboConsole from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/unplugin-turbo-console@1.10_5d2f29d59381ca626bc63b38f845472c/node_modules/unplugin-turbo-console/dist/vite.mjs";
import banner from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/vite-plugin-banner@0.7.1/node_modules/vite-plugin-banner/dist/index.mjs";
import boxen from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/boxen@8.0.1/node_modules/boxen/index.js";
import picocolors from "file:///E:/Workspace/new-media-management-front/node_modules/.pnpm/picocolors@1.1.0/node_modules/picocolors/picocolors.js";
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [
    vue(),
    vueJsx(),
    vueLegacy({
      renderLegacyChunks: false,
      modernPolyfills: [
        "es.array.at",
        "es.array.find-last"
      ]
    }),
    // https://github.com/vuejs/devtools-next
    viteEnv.VITE_OPEN_DEVTOOLS === "true" && VueDevTools(),
    // https://github.com/unplugin/unplugin-auto-import
    autoImport({
      imports: [
        "vue",
        "vue-router",
        "pinia"
      ],
      dts: "./src/types/auto-imports.d.ts",
      dirs: [
        "./src/utils/composables/**"
      ]
    }),
    // https://github.com/unplugin/unplugin-vue-components
    components({
      dirs: [
        "src/components",
        "src/layouts/ui-kit"
      ],
      include: [/\.vue$/, /\.vue\?vue/, /\.tsx$/],
      dts: "./src/types/components.d.ts"
    }),
    Unocss(),
    // https://github.com/vbenjs/vite-plugin-svg-icons
    createSvgIconsPlugin({
      iconDirs: [path.resolve(process.cwd(), "src/assets/icons/")],
      symbolId: "icon-[dir]-[name]",
      svgoOptions: isBuild
    }),
    // https://github.com/condorheroblog/vite-plugin-fake-server
    vitePluginFakeServer({
      logger: !isBuild,
      include: "src/mock",
      infixName: false,
      enableProd: isBuild && viteEnv.VITE_BUILD_MOCK === "true"
    }),
    // https://github.com/intlify/vue-i18n
    vueI18n({
      include: path.resolve(process.cwd(), "src/locales/lang/**")
    }),
    // https://github.com/dishait/vite-plugin-vue-meta-layouts
    Layouts({
      defaultLayout: "index"
    }),
    // https://github.com/hannoeru/vite-plugin-pages
    Pages({
      dirs: "src/views",
      exclude: [
        "**/components/**/*.vue"
      ]
    }),
    // https://github.com/nonzzz/vite-plugin-compression
    isBuild && viteEnv.VITE_BUILD_COMPRESS.split(",").includes("gzip") && compression(),
    isBuild && viteEnv.VITE_BUILD_COMPRESS.split(",").includes("brotli") && compression({
      exclude: [/\.(br)$/, /\.(gz)$/],
      algorithm: "brotliCompress"
    }),
    /* @__PURE__ */ function() {
      let outDir;
      return {
        name: "vite-plugin-archiver",
        apply: "build",
        configResolved(resolvedConfig) {
          outDir = resolvedConfig.build.outDir;
        },
        async closeBundle() {
          if (["zip", "tar"].includes(viteEnv.VITE_BUILD_ARCHIVE)) {
            await sleep(1e3);
            const archive = archiver(viteEnv.VITE_BUILD_ARCHIVE, {
              ...viteEnv.VITE_BUILD_ARCHIVE === "zip" && { zlib: { level: 9 } },
              ...viteEnv.VITE_BUILD_ARCHIVE === "tar" && { gzip: true, gzipOptions: { level: 9 } }
            });
            const output = fs.createWriteStream(`${outDir}.${dayjs().format("YYYY-MM-DD-HH-mm-ss")}.${viteEnv.VITE_BUILD_ARCHIVE === "zip" ? "zip" : "tar.gz"}`);
            archive.pipe(output);
            archive.directory(outDir, false);
            archive.finalize();
          }
        }
      };
    }(),
    function() {
      const virtualModuleId = "virtual:app-loading";
      const resolvedVirtualModuleId = `\0${virtualModuleId}`;
      return {
        name: "vite-plugin-loading",
        resolveId(id) {
          if (id === virtualModuleId) {
            return resolvedVirtualModuleId;
          }
        },
        load(id) {
          if (id === resolvedVirtualModuleId) {
            return {
              code: `
                export function loadingFadeOut() {
                  const loadingEl = document.querySelector('[data-app-loading]')
                  if (loadingEl) {
                    loadingEl.style['pointer-events'] = 'none'
                    loadingEl.style.visibility = 'hidden'
                    loadingEl.style.opacity = 0
                    loadingEl.style.transition = 'all 0.5s ease-out'
                    loadingEl.addEventListener('transitionend', () => loadingEl.remove(), { once: true })
                  }
                }
              `,
              map: null
            };
          }
        },
        enforce: "pre",
        transformIndexHtml: {
          handler: async (html) => html.replace(/<\/body>/, `${`<div data-app-loading>${await fs.readFileSync(path.resolve(process.cwd(), "loading.html"), "utf8")}</div>`}</body>`),
          order: "pre"
        }
      };
    }(),
    // https://github.com/unplugin/unplugin-turbo-console
    TurboConsole(),
    // https://github.com/chengpeiquan/vite-plugin-banner
    banner(`
/**
 * \u7531 Fantastic-admin \u63D0\u4F9B\u6280\u672F\u652F\u6301
 * Powered by Fantastic-admin
 * https://fantastic-admin.hurui.me
 */
    `),
    {
      name: "vite-plugin-debug-plugin",
      enforce: "pre",
      transform: (code, id) => {
        if (/src\/main.ts$/.test(id)) {
          if (viteEnv.VITE_APP_DEBUG_TOOL === "eruda") {
            code = code.concat(`
              import eruda from 'eruda'
              eruda.init()
            `);
          } else if (viteEnv.VITE_APP_DEBUG_TOOL === "vconsole") {
            code = code.concat(`
              import VConsole from 'vconsole'
              new VConsole()
            `);
          }
          return {
            code,
            map: null
          };
        }
      }
    },
    {
      name: "vite-plugin-disable-devtool",
      enforce: "pre",
      transform: (code, id) => {
        if (/src\/main.ts$/.test(id)) {
          if (viteEnv.VITE_APP_DISABLE_DEVTOOL === "true") {
            code = code.concat(`
              import DisableDevtool from 'disable-devtool'
              DisableDevtool()
            `);
          }
          return {
            code,
            map: null
          };
        }
      }
    },
    {
      name: "vite-plugin-terminal-info",
      apply: "serve",
      async buildStart() {
        const { bold, green, magenta, bgGreen, underline } = picocolors;
        console.log(
          boxen(
            `${bold(green(`\u7531 ${bgGreen("Fantastic-admin")} \u9A71\u52A8`))}

${underline("https://fantastic-admin.hurui.me")}

\u5F53\u524D\u4F7F\u7528\uFF1A${magenta("\u4E13\u4E1A\u7248")}`,
            {
              padding: 1,
              margin: 1,
              borderStyle: "double",
              textAlignment: "center"
            }
          )
        );
      }
    }
  ];
  return vitePlugins;
}

// vite.config.ts
var __vite_injected_original_dirname = "E:\\Workspace\\new-media-management-front";
var vite_config_default = async ({ mode, command }) => {
  const env = loadEnv(mode, process2.cwd());
  const scssResources = [];
  fs2.readdirSync("src/assets/styles/resources").forEach((dirname) => {
    if (fs2.statSync(`src/assets/styles/resources/${dirname}`).isFile()) {
      scssResources.push(`@use "src/assets/styles/resources/${dirname}" as *;`);
    }
  });
  return defineConfig({
    // 开发服务器选项 https://cn.vitejs.dev/config/server-options
    server: {
      open: true,
      host: "0.0.0.0",
      port: 9e3,
      proxy: {
        "/proxy": {
          target: env.VITE_APP_API_BASEURL,
          secure: false,
          changeOrigin: command === "serve" && env.VITE_OPEN_PROXY === "true",
          rewrite: (path3) => path3.replace(/\/proxy/, "")
        }
      }
    },
    // 构建选项 https://cn.vitejs.dev/config/build-options
    build: {
      outDir: mode === "production" ? "sgcc_front" : `sgcc_front-${mode}`,
      sourcemap: env.VITE_BUILD_SOURCEMAP === "true"
    },
    define: {
      __SYSTEM_INFO__: JSON.stringify({
        pkg: {
          version: package_default.version,
          dependencies: package_default.dependencies,
          devDependencies: package_default.devDependencies
        },
        lastBuildTime: dayjs2().format("YYYY-MM-DD HH:mm:ss")
      })
    },
    plugins: createVitePlugins(env, command === "build"),
    base: "./",
    resolve: {
      alias: {
        "@": path2.resolve(__vite_injected_original_dirname, "src"),
        "#": path2.resolve(__vite_injected_original_dirname, "src/types")
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: scssResources.join("")
        }
      }
    }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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

<route lang="yaml">
meta:
  title: 网站详情
</route>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import websiteManageApi from '@/api/modules/websiteManagement'

const { queryColumnData, executeCheck } = websiteManageApi
// 数据
const tableData = ref([])

const router = useRouter()
const route = useRoute()

// 新增多选相关数据和方法
const multipleSelection = ref<any[]>([])
const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val.length ? val.map(item => item.id) : []
  console.log('多选项变化:', multipleSelection.value)
}

onMounted(async () => {
  await getWebsiteColumnData(route.query.websiteId as string)
})

const getWebsiteColumnData = async (id: string) => {
  const res = await queryColumnData({
    network_id: id,
  })
  tableData.value = res?.data
  console.log('获取网站栏目详情数据', res)
}

const timelineName = (ruleType: string) => {
  if (ruleType === 'DAYS') return '自然日'
  if (ruleType === 'WORK_DAYS') return '工作日'
  if (ruleType === 'YEARS') return '年'
  if (ruleType === 'WEEKS') return '周'
  if (ruleType === 'MONTHS') return '月'
  if (ruleType === 'QUARTERS') return '季度'
}

// 日期相关函数和变量
const showDateModal = ref(false)
const dateRange: any = ref('')

// 获取当月1号到当天的日期范围
const getDefaultDateRange = () => {
  const today = new Date()
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)

  // 格式化为YYYY-MM-DD
  const formatDate = (date: Date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  return [
    formatDate(firstDayOfMonth),
    formatDate(today)
  ]
}

// 事件处理
const handleEdit = () => {
  console.log('编辑')
  router.push({
    name: 'websiteEdit',
    query: {
      websiteId: route.query.websiteId,
      networkName: route.query.networkName,
      networkType: route.query.networkType
    },
  })
}

const handleGoback = () => {
  console.log('返回')
  router.go(-1)
}

// 取消操作
const handleCancelDateSelect = () => {
  showDateModal.value = false
  dateRange.value = ''
}

// 确认执行检查
const handleConfirmCheck = async () => {
  // 如果用户没有选择日期，使用默认日期范围
  const dates = dateRange.value || getDefaultDateRange()

  // 构建请求参数
  const params = {
    website_id: route.query.websiteId as string,
    column_ids: multipleSelection.value,
    start_date: Array.isArray(dates) ? dates[0] : dates[0],
    end_date: Array.isArray(dates) ? dates[1] : dates[1]
  }

  console.log('请求参数:', params)

  const res: any = await executeCheck(params)
  console.log('执行检查结果', res)
  if ((res.code === 200 && res.res_code === 410)) {
    ElMessage.warning(res.message)
  } else if (res.code == 200&& res.res_code === '000000') {
    ElMessage.success(res.data || res.message || '执行检查成功')
  } else {
    ElMessage.error(res.data || res.message || '执行检查失败')
  }

  // 关闭弹框并清空日期
  showDateModal.value = false
  dateRange.value = ''
}

// 修改执行检查方法
const handleExecuteCheck = () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请选择需要执行检查的栏目')
    return
  }

  // 设置默认日期范围
  dateRange.value = getDefaultDateRange()

  // 显示日期选择器弹框
  showDateModal.value = true
}
</script>

<template>
  <PageMain>
    <div class="flex justify-between">
      <div class="font-600">
        {{ `${route.query.networkName}-${route.query.networkType}网站栏目详情如下` }}
      </div>
      <div class="flex items-center">
        <!-- <el-button type="primary" @click="handleEdit">编辑</el-button> -->
        <el-button type="primary"
          @click="handleExecuteCheck">执行</el-button>
        <el-button type="primary" @click="handleGoback">返回</el-button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="my-4">
      <el-table
        ref="tableRef"
        :data="tableData"
        border
        size="small"
        class="table-height-query"
        @selection-change="handleSelectionChange">
        <!-- 新增多选列 -->
        <el-table-column
          type="selection"
          width="55"
          align="center">
        </el-table-column>
        <el-table-column align="center" prop="column_type"
          label="栏目类型"></el-table-column>
        <el-table-column align="center" prop="first_column"
          label="一级栏目"></el-table-column>
        <el-table-column align="center" prop="second_column"
          label="二级栏目"></el-table-column>
        <el-table-column align="center" prop="third_column"
          label="三级栏目"></el-table-column>
        <el-table-column align="center" prop="ip_address"
          label="域名/IP"></el-table-column>
        <el-table-column align="center" prop="timeliness_rule_type"
          label="及时性要求（周期）">
          <template #default="scope">
            <span>{{ `每${timelineName(scope.row.timeliness_rule_type)}` }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="timeliness_rule"
          label="及时性要求（次数）">
          <template #default="scope">
            <span>{{ `${scope.row.timeliness_rule_count}次` }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 日期选择弹框 -->
    <el-dialog
      v-model="showDateModal"
      title="执行时间"
      width="30%"
      :before-close="handleCancelDateSelect"
    >
      <div class="flex justify-center items-center">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleConfirmCheck">确认</el-button>
          <el-button @click="handleCancelDateSelect">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </PageMain>
</template>

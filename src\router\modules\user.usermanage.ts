import type { RouteRecordRaw } from 'vue-router'
import { $t } from '@/locales'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/user',
  component: Layout,
  redirect: '/user/usermanage',
  name: 'user',
  meta: {
    title: $t('route.user.roleandusermanage'),
    icon: 'ep:user',
    defaultOpened : true,
    breadcrumb: false,
    auth: ['roleandusermanage']
  },
  children: [
    {
      path: 'rolemanage',
      name: 'roleManage',
      component: () => import('@/views/user/role_manage.vue'),
      meta: {
        menu: true,
        // activeMenu: '/rolemanage',
        title: $t('route.user.rolemanage'),
        auth: ['roleandusermanage.rolemanage']
      },
    },
    {
      path: 'usermanage',
      name: 'userManage',
      component: () => import('@/views/user/user_manage.vue'),
      meta: {
        menu: true,
        // activeMenu: '/usermanage',
        title: $t('route.user.usermanage'),
        auth: ['roleandusermanage.usermanage']
      },
    },
    {
      path: 'usergroupmanage',
      name: 'userGroupManage',
      component: () => import('@/views/user/user_group_manage.vue'),
      meta: {
        menu: true,
        title: $t('route.user.usergroupmanage'),
        auth: ['roleandusermanage.usergroup']
      },
    },
  ],
}

export default routes

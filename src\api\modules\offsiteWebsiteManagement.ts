import api from '../index'

export default {
  // 查看站外网站名单
  queryOffsiteWebsites: (network_info_id: string, params?: {
    page_index?: number,
    page_size?: number,
  }) => api.get(`/offsite/${network_info_id}`, {
    params: {
      ...params,
    },
  }),

  // 新增站外网站
  addOffsiteWebsites: (network_info_id: string, data: {
    websites: Array<{
      name?: string,
      network_type?: string,
      url: string
    }>
  }) => api.post(`/offsite/${network_info_id}`, data),

  // 修改站外网站
  updateOffsiteWebsite: (network_info_id: string, data: {
    id: string,
    name?: string,
    url?: string
  }) => api.put(`/offsite/${network_info_id}`, data),

  // 删除站外网站
  deleteOffsiteWebsites: (network_info_id: string, ids: string) =>
    api.delete(`/offsite/${network_info_id}?ids=${ids}`),

  // 上传excel
  uploadOffsiteWebsiteExcel: (network_info_id: string, formData: FormData) => {
    return api.post(`/offsite/excel/${network_info_id}`, formData);
  },

  // 下载excel
  downloadOffsiteWebsiteExcel: (network_info_id: string) => {
    return api.get(`/offsite/excel/${network_info_id}`, {
      headers: {
        'Content-Type': 'application/octet-stream'
      },
      responseType: 'blob'
    });
  }
}
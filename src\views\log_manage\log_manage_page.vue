<route lang="yaml">
  meta:
    title: 日志管理
</route>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import logManageApi from '@/api/modules/logManagement'

const { queryLogFiles, downloadLogFile, deleteLogFiles } = logManageApi

// 数据
const tableData = ref([])

// 分页相关变量
const total = ref(0)
const currentPage = ref(1)
const pageCount = ref(0)
const pageSize = ref(20)

// 获取日志文件列表
const getLogFileData = async () => {
  try {
    const res: any = await queryLogFiles({
      page_index: currentPage.value,
      page_size: pageSize.value
    })
    if (res.code === 200) {
      tableData.value = res.data.page_list || []
      total.value = res.data.total_num || 0
      pageCount.value = res.data.page_num || 0
      console.log('获取日志文件列表成功', tableData.value)
    } else {
      ElMessage.error(res.message || '获取日志文件列表失败')
    }
  } catch (error) {
    console.error('获取日志文件列表失败', error)
    ElMessage.error('获取日志文件列表失败')
  }
}

// 处理页码变化
const handlePageChange = async (val: number) => {
  currentPage.value = val
  await getLogFileData()
}

// 处理每页数量变化
const handleSizeChange = async (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  await getLogFileData()
}

// 页面初始化时获取数据
onMounted(async () => {
  await getLogFileData()
})

// 下载日志文件
const handleDownload = async (index: number, row: any) => {
  console.log(`下载 - ${index}`, row)
  try {
    const res: any = await downloadLogFile(row.file_name)
    const blob = new Blob([res.data], { type: 'application/octet-stream' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = row.file_name
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败', error)
    ElMessage.error('下载失败')
  }
}

// 删除日志文件
const handleDelete = (index: number, row: any) => {
  console.log(`删除 - ${index}`, row)

  // 添加确认弹窗
  ElMessageBox.confirm(
    '是否确认删除该日志文件？',
    '警告',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      // 用户点击确认，执行删除操作
      const res: any = await deleteLogFiles([row.file_name])
      console.log(res)
      if (res.code === 200) {
        if (res.res_code !== '000000') {
          ElMessage.warning(res.message || '删除失败')
          return
        }
        ElMessage.success('删除成功')
        // 刷新日志列表
        await getLogFileData()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    })
    .catch(() => {
      // 用户点击取消，不执行任何操作
      ElMessage.info('已取消删除')
    })
}
</script>

<template>
  <PageMain>
    <!-- 表格 -->
     <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-bold mb-4">日志列表</h2>
      </div>
    <div class="my-4">
      <el-table :data="tableData" border size="small" class="table-height-query">
        <el-table-column align="center" type="index" label="序号" width="80"></el-table-column>
        <el-table-column align="center" prop="file_name" label="日志名称" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="操作" width="200">
          <template #default="scope">
            <div class="flex justify-center flex-wrap">
              <el-button size="small" color="#00706B" @click="handleDownload(scope.$index, scope.row)" v-auth="['logmanage.download']">下载</el-button>
              <!-- <el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button> -->
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 翻页组件 -->
    <div class="flex justify-end">
      <el-pagination
        background
        layout="sizes, total, prev, pager, next"
        :page-size="pageSize"
        :page-sizes="[20, 30, 40, 50, 100]"
        :total="total"
        :page-count="pageCount"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      ></el-pagination>
    </div>
  </PageMain>
</template>
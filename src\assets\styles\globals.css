/* 页面布局 CSS 变量 */
:root {
  /**
   * 这是一个复合变量
   * 当页宽模式为 adaption-min-width 时，它代表 最小宽度
   * 当页宽模式为 center 时，它代表 固定宽度
   * 当页宽模式为 center-max-width 时，它代表 最大宽度
   */
  --g-app-width: 1400px;

  /* 头部宽度（默认自适应宽度，可固定宽度，固定宽度后为居中显示） */
  --g-header-width: 100%;

  /* 头部高度 */
  --g-header-height: 60px;

  /* 侧边栏宽度 */
  --g-main-sidebar-width: 80px;
  --g-sub-sidebar-width: 220px;
  --g-sub-sidebar-collapse-width: 64px;

  /* 侧边栏 Logo 区域高度 */
  --g-sidebar-logo-height: 50px;

  /* 标签栏高度 */
  --g-tabbar-height: 50px;

  /* 工具栏高度 */
  --g-toolbar-height: 50px;

  /* 标签页最大最小宽度，两个宽度为同一数值时，则为固定宽度，反之将宽度设置为 unset 则为自适应 */
  --g-tabbar-tab-max-width: 150px;
  --g-tabbar-tab-min-width: 150px;
}

/* 明暗模式 CSS 变量 */
/* stylelint-disable-next-line no-duplicate-selectors */
:root {
  color-scheme: light;

  --g-box-shadow-color: rgb(0 0 0 / 12%);

  &::view-transition-old(root),
  &::view-transition-new(root) {
    mix-blend-mode: normal;
    animation: none;
  }

  &::view-transition-old(root) {
    z-index: 1;
  }

  &::view-transition-new(root) {
    z-index: 9999;
  }

  &.dark {
    color-scheme: dark;

    --g-box-shadow-color: rgb(0 0 0 / 72%);

    &::view-transition-old(root) {
      z-index: 9999;
    }

    &::view-transition-new(root) {
      z-index: 1;
    }
  }
}

::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: rgb(0 0 0 / 40%);
  background-clip: padding-box;
  border: 3px solid transparent;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(0 0 0 / 50%);
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

html,
body {
  height: 100%;
}

body {
  box-sizing: border-box;
  margin: 0;
  font-family: Lato, "PingFang SC", "Microsoft YaHei", sans-serif;
  background-color: var(--g-container-bg);
  -webkit-tap-highlight-color: transparent;

  &.overflow-hidden {
    overflow: hidden;
  }
}

* {
  box-sizing: inherit;
}

#app {
  height: 100%;
}

/* 右侧内容区针对fixed元素，有横向铺满的需求，可在fixed元素上设置 [data-fixed-calc-width] */
[data-fixed-calc-width] {
  position: fixed;
  inset-inline: 50% 0;
  width: calc(100% - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
  transform: translateX(-50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2));

  [data-app-width-mode="center"] & {
    max-width: calc(var(--g-app-width) - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
  }

  [data-app-width-mode="center-max-width"] & {
    max-width: calc(var(--g-app-width) - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
  }

  [dir="rtl"] & {
    transform: translateX(50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2 * -1)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2 * -1));
  }

  [data-mode="mobile"] & {
    width: 100% !important;
    transform: translateX(-50%) !important;

    [dir="rtl"] & {
      transform: translateX(50%) !important;
    }
  }
}

/* textarea 字体跟随系统 */
textarea {
  font-family: inherit;
}

/* Overrides Floating Vue */
.v-popper--theme-dropdown,
.v-popper--theme-tooltip {
  --uno: inline-flex;
}

.v-popper--theme-dropdown .v-popper__inner,
.v-popper--theme-tooltip .v-popper__inner {
  --uno: bg-white dark-bg-stone-8 text-dark dark-text-white rounded shadow ring-1 ring-gray-200 dark-ring-gray-800 border border-solid border-stone/20 text-xs font-normal;

  box-shadow: 0 6px 30px rgb(0 0 0 / 10%);
}

.v-popper--theme-tooltip .v-popper__arrow-inner,
.v-popper--theme-dropdown .v-popper__arrow-inner {
  visibility: visible;

  --uno: border-white dark-border-stone-8;
}

.v-popper--theme-tooltip .v-popper__arrow-outer,
.v-popper--theme-dropdown .v-popper__arrow-outer {
  --uno: border-stone/20;
}

.v-popper--theme-tooltip.v-popper--shown,
.v-popper--theme-tooltip.v-popper--shown * {
  transition: none !important;
}

[data-overlayscrollbars-contents] {
  overscroll-behavior: contain;
}

/* medium-zoom */
.medium-zoom-overlay,
.medium-zoom-image {
  z-index: 3000;
}

/* 在全局CSS中覆盖表格头颜色 */
.el-table th,
.vxe-table--header {
  color: #333 !important;
  background-color: #e8e8e8 !important;
}

/* 全局表格高度设置  带查询条件高度 */
.table-height {
  height: calc(100vh - 310px) !important;
}

/* 全局表格高度设置  不带查询条件高度 */
.table-height-query {
  height: calc(100vh - 260px) !important;
}

import type { RouteRecordRaw } from 'vue-router'
import { $t } from '@/locales'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/chekresult',
  component: Layout,
  redirect: '/chekresult/reultview',
  name: 'chekResult',
  meta: {
    title: $t('route.checkresult.root'),
    icon: 'ep:document',
    defaultOpened : true,
  },
  children: [
    {
      path: 'reultview',
      name: 'resultView',
      component: () => import('@/views/check_result/check_result_view.vue'),
      meta: {
        title: $t('route.checkresult.resultview'),
      },
    },
  ],
}

export default routes

import { cloneDeep } from 'lodash-es'
import { createDefu } from 'defu'
import useSettingsStore from './settings'
import useTabbarStore from './tabbar'
import useRouteStore from './route'
import useMenuStore from './menu'
import router from '@/router'
import type { Settings } from '#/global'
import apiUser from '@/api/modules/user'
import roleManageApi from '@/api/modules/roleManage'
import storage from '@/utils/storage'
import settingsDefault from '@/settings'
import { ElMessage } from 'element-plus'

enum Role {
  SYSTEM_ADMIN = 'SYSTEM_ADMIN', // 超级管理员
  CONFIG_ADMIN = 'CONFIG_ADMIN', // 配置管理员
  NORMAL = 'NORMAL', // 普通用户
  AUDITOR = 'AUDITOR', // 审计账户
}

type RoleType = 'SYSTEM_ADMIN' | 'CONFIG_ADMIN' | 'NORMAL' | 'AUDITOR'

const useUserStore = defineStore(
  // 唯一ID
  'user',
  () => {
    const settingsStore = useSettingsStore()
    const tabbarStore = useTabbarStore()
    const routeStore = useRouteStore()
    const menuStore = useMenuStore()

    // 以下为此前端框架在静态无后端服务模拟状态下的登录状态和数据
    const account = ref(storage.local.get('account') ?? '')
    const token = ref(storage.local.get('token') ?? '')
    const avatar = ref(storage.local.get('avatar') ?? '')
    const permissions = ref<string[]>([])
    // 权限版本号，用于触发权限相关组件的更新
    const permissionVersion = ref(0)
    //以下为本项目有后端服务动态获取数据状态下的登录状态和数据
    const name = ref(storage.local.get('name') ?? '')
    const bladeAuth = ref(storage.local.get('blade-auth') ?? '')
    const role = ref(storage.local.get('role') ?? '')
    const role_id = ref(storage.local.get('role_id') ?? '')
    const chinese_name = ref(storage.local.get('chinese_name') ?? '')
    const unit_name = ref(storage.local.get('unit_name') ?? '')

    const isLogin = computed(() => {
      // 判断token有值是在本前端框架模拟状态下的登录，判断bladeAuth有值是在本项目有后端服务动态获取数据状态下的登录
      if (token.value || bladeAuth.value) {
        return true
      }
      return false
    })

    async function SGCCLogin(data: { name: string, password: string, captcha: string, captcha_id: string }) {
      const res: any = await apiUser.sgccLogin(data)
      if (res.code === 200 && res.res_code !== '000000') {
        return ElMessage.warning(res.message)
      }
      storage.local.set('name', res.data.username)
      storage.local.set('blade-auth', res.data.Authorization)
      storage.local.set('role', res.data.role)
      storage.local.set('role_id', res.data.role_id)
      storage.local.set('chinese_name', res.data.chinese_name)
      storage.local.set('unit_name', res.data.unit_name)
      name.value = res.data.username
      bladeAuth.value = res.data.Authorization
      role.value = res.data.role
      // 清空权限，等待后续通过getPermissions获取
      updatePermissions([])
      role_id.value = res.data.role_id
      chinese_name.value = res.data.chinese_name
      unit_name.value = res.data.unit_name
    }

    // 国网不走登录页，走单点登录
    async function SGCCSSOLogin(data: { token: string, check: string }) {
      const res: any = await apiUser.sgccSSOLogin(data)
      if (res.code === 200 && res.res_code !== '000000') {
        return ElMessage.warning(res.message)
      }
      storage.local.set('name', res.data.username)
      storage.local.set('blade-auth', res.data.Authorization)
      storage.local.set('role', res.data.role)
      storage.local.set('role_id', res.data.role_id)
      storage.local.set('chinese_name', res.data.chinese_name)
      storage.local.set('unit_name', res.data.unit_name)
      name.value = res.data.username
      bladeAuth.value = res.data.Authorization
      role.value = res.data.role
      // 清空权限，等待后续通过getPermissions获取
      updatePermissions([])
      role_id.value = res.data.role_id
      chinese_name.value = res.data.chinese_name
      unit_name.value = res.data.unit_name

      // 智能跳转到正确的业务页面
      await navigateToBusinessPage()
    }

    // 智能跳转到业务页面的函数
    async function navigateToBusinessPage() {
      try {
        // 等待权限获取完成
        if (settingsStore.settings.app.enablePermission) {
          await getPermissions()
        }

        // 导入路由模块以获取业务路由
        const routeStore = useRouteStore()

        // 如果路由还未生成，先生成路由
        if (!routeStore.isGenerate) {
          // 根据配置生成路由
          switch (settingsStore.settings.app.routeBaseOn) {
            case 'frontend':
              const { asyncRoutes } = await import('@/router/routes')
              routeStore.generateRoutesAtFront(asyncRoutes)
              break
            case 'backend':
              await routeStore.generateRoutesAtBack()
              break
            case 'filesystem':
              const { asyncRoutesByFilesystem } = await import('@/router/routes')
              routeStore.generateRoutesAtFilesystem(asyncRoutesByFilesystem)
              break
          }
        }

        // 等待一个tick确保菜单数据已更新
        await nextTick()

        // 获取第一个有权限的业务页面路径
        const targetPath = getFirstAvailableBusinessPath()

        if (targetPath) {
          console.log('SSO登录成功，跳转到业务页面:', targetPath)
          router.replace({ path: targetPath })
        } else {
          // 如果没有可用的业务页面，跳转到配置管理（作为默认页面）
          console.log('SSO登录成功，没有可用业务页面，跳转到默认页面')
          router.replace({ path: '/config/unitmanage' })
        }
      } catch (error) {
        console.error('SSO登录跳转失败:', error)
        // 发生错误时，跳转到配置管理页面作为安全选择
        router.replace({ path: '/config/unitmanage' })
      }
    }

    // 获取第一个可用的业务页面路径
    function getFirstAvailableBusinessPath(): string | null {
      const menuStore = useMenuStore()

      // 预定义的业务页面优先级列表（按重要性排序）
      const businessPaths = [
        '/checkconfigmanagement/websitecolumnmanagement',  // 网站栏目管理
        '/temporarytask/tasklist',                         // 临时任务管理
        '/reportquery/historyreportquery',                 // 历史报告查询
        '/user/usermanage',                                // 用户管理
        '/log/logmanage',                                  // 日志管理
        '/config/unitmanage',                              // 单位管理
      ]

      // 检查每个业务页面是否有权限访问
      for (const path of businessPaths) {
        if (hasAccessToPath(path)) {
          return path
        }
      }

      // 如果预定义路径都没有权限，尝试从菜单中获取第一个可用路径
      if (menuStore.sidebarMenus.length > 0) {
        return menuStore.sidebarMenusFirstDeepestPath
      }

      return null
    }

    // 检查是否有访问指定路径的权限
    function hasAccessToPath(path: string): boolean {
      if (!settingsStore.settings.app.enablePermission) {
        return true
      }

      // 路径到权限的映射
      const pathPermissionMap: Record<string, string[]> = {
        '/checkconfigmanagement/websitecolumnmanagement': ['websitemanage'],
        '/temporarytask/tasklist': ['temporarytask'],
        '/reportquery/historyreportquery': ['historyreportquery'],
        '/user/usermanage': ['usermanage'],
        '/log/logmanage': ['logmanage'],
        '/config/unitmanage': ['configmanage.unitmanage'],
      }

      const requiredPermissions = pathPermissionMap[path]
      if (!requiredPermissions) {
        return true // 如果没有定义权限要求，默认允许访问
      }

      // 检查用户是否有任一所需权限
      return requiredPermissions.some(permission => permissions.value.includes(permission))
    }
    // 登录
    async function login(data: {
      account: string
      password: string
    }) {
      const res = await apiUser.login(data)
      storage.local.set('account', res.data.account)
      storage.local.set('token', res.data.token)
      storage.local.set('avatar', res.data.avatar)
      storage.local.set('name', res.data.name)
      storage.local.set('blade-auth', res.data.bladeAuth)
      storage.local.set('role', res.data.role)
      name.value = res.data.username
      bladeAuth.value = res.data.Authorization
      role.value = res.data.role
      updatePermissions([res.data.role] as RoleType[])
      account.value = res.data.account
      token.value = res.data.token
      avatar.value = res.data.avatar
    }
    // 登出
    async function logout(redirect = router.currentRoute.value.fullPath) {
      tabbarStore.clean()
      routeStore.removeRoutes()
      menuStore.setActived(0)
      //下面六行是针对本前端框架在静态无后端服务模拟状态下的登出
      storage.local.remove('account')
      storage.local.remove('token')
      storage.local.remove('avatar')
      account.value = ''
      token.value = ''
      avatar.value = ''
      // 下面四行是针对本项目的有后端服务动态获取数据状态下的登出
      storage.local.remove('name')
      storage.local.remove('blade-auth')
      storage.local.remove('role')
      storage.local.remove('role_id')
      storage.local.remove('chinese_name')
      storage.local.remove('unit_name')
      storage.local.remove('permissions')
      name.value = ''
      bladeAuth.value = ''
      role.value = ''
      role_id.value = ''
      chinese_name.value = ''
      unit_name.value = ''
      updatePermissions([])
      if (import.meta.env.DEV) { // 开发环境跳转到登录页
        // window.location.href = 'http://iscsso.sgcc.com.cn/isc_sso/login'
        router.push({
          name: 'login',
          // query: {
          //   ...(redirect !== settingsStore.settings.home.fullPath && router.currentRoute.value.name !== 'login' && { redirect }),
          // },
        })
      } else { // 由于系统内嵌到客户系统中，所以需要跳转到客户系统的登录页
        window.location.href = 'http://20.1.62.152:28080/productionOperationsWorkbench/personalWorkbench'
      }
    }
    // 更新权限并触发响应式更新
    function updatePermissions(newPermissions: string[]) {
      permissions.value = newPermissions
      permissionVersion.value++
      storage.local.set('permissions', JSON.stringify(permissions.value))

      // 触发全局事件，通知所有权限相关组件更新
      nextTick(() => {
        // 强制触发所有使用权限的组件重新渲染
        document.dispatchEvent(new CustomEvent('permissions-updated', {
          detail: { permissions: newPermissions, version: permissionVersion.value }
        }))
      })
    }

    // 获取权限
    async function getPermissions() {
      // const res = await apiUser.permission()
      // permissions.value = res.data.permissions
      // const role: string = storage.local.get('role') as string
      // permissions.value = [role] as RoleType[]

      // 获取用户权限列表
      const permissionRes: any = await roleManageApi.getRolePermissionCodenames(role_id.value)
      if (permissionRes.code === 200 && permissionRes.res_code !== '000000') {
        return ElMessage.warning(permissionRes.message)
      }
      const newPermissions = [...permissionRes.data, role.value]
      updatePermissions(newPermissions)
    }

    // 修改密码
    async function editPassword(data: {
      password: string
      newpassword: string
    }) {
      await apiUser.passwordEdit(data)
    }

    // 框架已将可提供给用户配置的选项提取出来，请勿新增其他选项，不需要的选项可以在这里注释掉
    const preferences = ref<Settings.all>({
      app: {
        colorScheme: settingsDefault.app.colorScheme,
        lightTheme: settingsDefault.app.lightTheme,
        darkTheme: settingsDefault.app.darkTheme,
        enableColorAmblyopiaMode: settingsDefault.app.enableColorAmblyopiaMode,
        enableProgress: settingsDefault.app.enableProgress,
        defaultLang: settingsDefault.app.defaultLang,
      },
      menu: {
        mode: settingsDefault.menu.mode,
        style: settingsDefault.menu.style,
        isRounded: settingsDefault.menu.isRounded,
        switchMainMenuAndPageJump: settingsDefault.menu.switchMainMenuAndPageJump,
        subMenuUniqueOpened: settingsDefault.menu.subMenuUniqueOpened,
        subMenuCollapse: settingsDefault.menu.subMenuCollapse,
        subMenuAutoCollapse: settingsDefault.menu.subMenuAutoCollapse,
        enableSubMenuCollapseButton: settingsDefault.menu.enableSubMenuCollapseButton,
      },
      layout: {
        widthMode: settingsDefault.layout.widthMode,
      },
      mainPage: {
        enableTransition: settingsDefault.mainPage.enableTransition,
        transitionMode: settingsDefault.mainPage.transitionMode,
      },
      topbar: {
        mode: settingsDefault.topbar.mode,
        switchTabbarAndToolbar: settingsDefault.topbar.switchTabbarAndToolbar,
      },
      tabbar: {
        style: settingsDefault.tabbar.style,
        enableIcon: settingsDefault.tabbar.enableIcon,
        dblclickAction: settingsDefault.tabbar.dblclickAction,
        enableMemory: settingsDefault.tabbar.enableMemory,
      },
      toolbar: {
        breadcrumb: settingsDefault.toolbar.breadcrumb,
        navSearch: settingsDefault.toolbar.navSearch,
        fullscreen: settingsDefault.toolbar.fullscreen,
        pageReload: settingsDefault.toolbar.pageReload,
        colorScheme: settingsDefault.toolbar.colorScheme,
        layout: settingsDefault.toolbar.layout,
      },
      breadcrumb: {
        style: settingsDefault.breadcrumb.style,
        enableMainMenu: settingsDefault.breadcrumb.enableMainMenu,
      },
    })
    // 此处没有使用 lodash 的 defaultsDeep 函数，而是基于 defu 库自定义了一个函数，只合并 settings 中有的属性，而不是全部合并，这样做的目的是为了排除用户历史遗留的偏好配置
    const customDefaultsDeep = createDefu((obj, key, value) => {
      if (obj[key] === undefined) {
        delete obj[key]
        return true
      }
      if (Array.isArray(obj[key]) && Array.isArray(value)) {
        obj[key] = value
        return true
      }
    })
    // isPreferencesUpdating 用于防止循环更新
    let isPreferencesUpdating = false
    watch(preferences, (val) => {
      if (!settingsStore.settings.userPreferences.enable) {
        return
      }
      if (!isPreferencesUpdating) {
        isPreferencesUpdating = true
        settingsStore.updateSettings(cloneDeep(val))
      }
      else {
        isPreferencesUpdating = false
      }
      updatePreferences(cloneDeep(val))
    }, {
      deep: true,
    })
    watch(() => settingsStore.settings, (val) => {
      if (!settingsStore.settings.userPreferences.enable) {
        return
      }
      if (!isPreferencesUpdating) {
        isPreferencesUpdating = true
        preferences.value = customDefaultsDeep(val, preferences.value)
      }
      else {
        isPreferencesUpdating = false
      }
    }, {
      deep: true,
    })
    // isPreferencesInited 用于防止初始化时触发更新
    let isPreferencesInited = false
    // 获取偏好设置
    async function getPreferences() {
      let data: Settings.all = {}
      if (settingsStore.settings.userPreferences.storageTo === 'local') {
        if (storage.local.has('userPreferences')) {
          data = JSON.parse(storage.local.get('userPreferences') as string)[account.value] || {}
        }
      }
      else if (settingsStore.settings.userPreferences.storageTo === 'server') {
        const res = await apiUser.preferences()
        data = JSON.parse(res.data.preferences || '{}') as Settings.all
      }
      preferences.value = customDefaultsDeep(data, preferences.value)
    }
    // 更新偏好设置
    async function updatePreferences(data: Settings.all = {}) {
      if (!isPreferencesInited) {
        isPreferencesInited = true
        return
      }
      if (settingsStore.settings.userPreferences.storageTo === 'local') {
        const userPreferencesData = storage.local.has('userPreferences') ? JSON.parse(storage.local.get('userPreferences') as string) : {}
        userPreferencesData[account.value] = data
        storage.local.set('userPreferences', JSON.stringify(userPreferencesData))
      }
      else if (settingsStore.settings.userPreferences.storageTo === 'server') {
        await apiUser.preferencesEdit(JSON.stringify(data))
      }
    }

    // 获取用户列表
    async function getUserList(params: {
      page_index?: number,
      page_size?: number,
    } = {}) {
      try {
        const res: any = await apiUser.getUserList(params)
        if (res.code === 200 && res.res_code === '000000') {
          return res.data
        } else {
          ElMessage.warning(res.message || '获取用户列表失败')
          return null
        }
      } catch (error) {
        ElMessage.error('获取用户列表失败')
        return null
      }
    }

    // 新增用户
    async function addUser(data: {
      name: string
      role_id: string,
      password?: string,
      real_name: string,
      belonging_unit_id: string, // 添加单位ID字段
      phone: string,
      email: string,
    }) {
      try {
        const res: any = await apiUser.addUser(data)
        if (res.code === 200 && res.res_code !== '000000') {
          ElMessage.warning(res.message || '新增用户失败')
          return false
        } else if (res.code === 200) {
          ElMessage.success('新增用户成功')
          // 重新获取列表
          return await getUserList()
        } else {
          ElMessage.error(res.data || '新增用户失败')
          return false
        }
      } catch (error) {
        ElMessage.error('新增用户失败')
        return false
      }
    }

    // 删除用户
    async function deleteUser(id: string) {
      try {
        const res: any = await apiUser.deleteUser(id)
        if (res.code === 200) {
          if (res.res_code !== '000000') {
            ElMessage.warning(res.message || '删除用户失败')
            return false
          } else {
            ElMessage.success(res.data || res.message || '删除用户成功')
            // 重新获取列表
            return await getUserList()
          }
        } else {
          ElMessage.error(res.data || '删除用户失败')
          return false
        }
      } catch (error) {
        ElMessage.error('删除用户失败')
        return false
      }
    }

    // 查看用户网站
    async function getUserNetworks(userId: string, shareFlag: string = '0') {
      try {
        const res: any = await apiUser.getUserNetworks(userId, shareFlag)
        if (res.code === 200 && res.res_code === '000000') {
          return res.data || []
        } else {
          ElMessage.error(res.message || '获取用户网站列表失败')
          return []
        }
      } catch (error) {
        ElMessage.error('获取用户网站列表失败')
        return []
      }
    }

    // 给用户分配网站
    async function assignNetworksToUser(userId: string, data: { network_info_ids: string[] }) {
      try {
        const res: any = await apiUser.assignNetworksToUser(userId, data)
        if (res.code === 200 && res.res_code === '000000') {
          ElMessage.success(res.message || '分配网站成功')
          return true
        } else {
          ElMessage.error(res.message || '分配网站失败')
          return false
        }
      } catch (error) {
        ElMessage.error('分配网站失败')
        return false
      }
    }

    // 取消分配给用户的网站
    async function removeNetworksFromUser(userId: string, data: { network_info_ids: string[] }) {
      try {
        const res: any = await apiUser.removeNetworksFromUser(userId, data)
        if (res.code === 200 && res.res_code === '000000') {
          ElMessage.success(res.message || '取消分配网站成功')
          return true
        } else {
          ElMessage.error(res.message || '取消分配网站失败')
          return false
        }
      } catch (error) {
        ElMessage.error('取消分配网站失败')
        return false
      }
    }

    // 修改用户
    async function updateUser(data: {
      id: string
      name: string
      role_id: string
      real_name: string
      belonging_unit_id: string
      phone: string
      email: string
      // id_card: string;
    }) {
      try {
        const res: any = await apiUser.updateUser(data)
        if (res.code === 200 && res.res_code !== '000000') {
          ElMessage.warning(res.message || '修改用户失败')
          return false
        } else if (res.code === 200) {
          ElMessage.success('修改用户成功')
          // 重新获取列表
          return await getUserList()
        } else {
          ElMessage.error(res.data || '修改用户失败')
          return false
        }
      } catch (error) {
        ElMessage.error('修改用户失败')
        return false
      }
    }

    return {
      account,
      token,
      avatar,
      permissions,
      permissionVersion,
      updatePermissions,
      isLogin,
      name,
      chinese_name,
      unit_name,
      bladeAuth,
      login,
      logout,
      getPermissions,
      editPassword,
      preferences,
      getPreferences,
      updatePreferences,
      SGCCLogin,
      SGCCSSOLogin,
      getUserList,
      addUser,
      deleteUser,
      getUserNetworks,
      assignNetworksToUser,
      removeNetworksFromUser,
      updateUser
    }
  },
)

export default useUserStore

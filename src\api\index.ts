import axios from 'axios'
import CryptoJS from 'crypto-js'

// import qs from 'qs'
import Message from 'vue-m-message'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import storage from '@/utils/storage'
import apiUser from '@/api/modules/user'

let isRefreshToken = true
let $401 = false

const api = axios.create({
  baseURL: (import.meta.env.DEV && import.meta.env.VITE_OPEN_PROXY === 'true') ? '/proxy/' : import.meta.env.VITE_APP_API_BASEURL,
  timeout: 1000 * 60,
  responseType: 'json',
})

api.interceptors.request.use(
  (request) => {
    // 全局拦截请求发送前提交的参数
    const settingsStore = useSettingsStore()
    const userStore = useUserStore()
    // 设置请求头
    if (request.headers) {
      request.headers['Accept-Language'] = settingsStore.lang
      const timestamp = Date.now()  // 获取当前毫秒级时间戳
      const publicKey = 'DKnls"L!#KJNDW)#NDSJjsdnwb546ew2' // 获取生成csrftoken所需要的写死的公钥
      console.log('key', publicKey)
      const csrftoken = CryptoJS.SHA512(`${timestamp}${publicKey}`).toString()  // 使用公钥加密时间戳利用SHA512生成csrftoken
      request.headers['X-CSRFToken'] = csrftoken
      request.headers['Timestamp'] = timestamp
      if (userStore.isLogin) {
        // 判断localStorage中、以及Pinia中是否存在用户blade-auth，如果存在则将blade-auth添加到请求头，此判断分支是针对本项目有后端动态支持的情况下的
        if (storage.local.has('blade-auth') && userStore.bladeAuth) {
          request.headers['blade-auth'] = storage.local.get('blade-auth')
        } else {
          // 此判断分支是针对本项目没有后端动态支持的情况下的
          request.headers.Token = userStore.token
        }
      }
    }
    // 是否将 POST 请求参数进行字符串化处理
    if (request.method === 'post') {
      // request.data = qs.stringify(request.data, {
      //   arrayFormat: 'brackets',
      // })
    }
    return request
  },
)

api.interceptors.response.use(
  (response) => {
    console.log('response', response)

    //刷新token
    if ((response.headers["Refresh-Token"] === "true" || response.headers["refresh-token"] === "true") && isRefreshToken) {
      console.log('刷新token---------')
      isRefreshToken = false
      apiUser.refreshToken().then((res: any) => {
        isRefreshToken = true
        storage.local.set('blade-auth', res.data.Authorization)
      }).catch(() => {
        isRefreshToken = true
      })
    }
    // 针对本项目用户token失效进行的处理
    if (response.data.code === 401 || response.status === 401) {
      Message.warning(response.data.message || '登录状态失效，请重新登录')
      if (!$401) {
        console.log('触发跳转')
        $401 = true;
        useUserStore().logout(); //清除token
        setTimeout(() => {
          $401 = false;
        }, 5000);
      }
      // useUserStore().logout()
      return Promise.reject(response.data)
    }

    if (response.data.code === 403 || response.status === 403) {
      Message.warning(response.data.message || '登录状态失效，请重新登录')
      // useUserStore().logout()
      return Promise.reject(response.data)
    }
      // 对本项目业务的状态码进行判断，跟这个前端框架的状态码约定不同，这里需要做一个转换
    if (response.data.code !== 200 && response.config.responseType !== 'blob' && response.headers.content_type !== 'application/octet-stream') {
      if (response.data.code === 401 || response.status === 401) {
        Message.warning('登录状态失效，请重新登录')
        // useUserStore().logout()
        return Promise.reject(response.data)
      }
      Message.warning(response.data.message)
      return Promise.reject(response.data)
    }
    /**
     * 全局拦截请求发送后返回的数据，如果数据有报错则在这做全局的错误提示
     * 假设返回数据格式为：{ status: 1, error: '', data: '' }
     * 规则是当 status 为 1 时表示请求成功，为 0 时表示接口需要登录或者登录状态失效，需要重新登录
     * 请求出错时 error 会返回错误信息
     */
    if (response.config.responseType !== 'blob' && response.headers.content_type !== 'application/octet-stream') {
      if (response.data.status === 1 || response.status === 200 || response.data.code === 200) {
        if (response.data.error) {
          // 错误提示
          Message.error(response.data.error, {
            zIndex: 2000,
          })
          return Promise.reject(response.data)
        }
        return Promise.resolve(response.data)
      }
      else {
        // useUserStore().logout()
      }
      return Promise.resolve(response.data)
    }
    // 如果是下载栏目模板则直接返回，因为要获取header里面的文件名称
    if (response.request.responseType === 'blob') {
      return Promise.resolve(response)
    }
    return Promise.resolve(response.data)
  },
  (error) => {
    if (error.response && (error.response.status === 401 || error.response.data.code === 401)) {
      Message.warning('登录状态失效，请重新登录')
      if (!$401) {
        console.log('触发跳转')
        $401 = true;
        useUserStore().logout(); //清除token
        setTimeout(() => {
          $401 = false;
        }, 5000);
      }
      return Promise.reject(error)
    }
    let message = error.message
    if (message === 'Network Error') {
      message = '后端网络故障'
    }
    else if (message.includes('timeout')) {
      message = '接口请求超时'
    }
    else if (message.includes('Request failed with status code')) {
      message = `接口${message.substr(message.length - 3)}异常`
    }
    Message.error(message, {
      zIndex: 2000,
    })
    // useUserStore().logout()
    return Promise.reject(error)
  },
)

export default api

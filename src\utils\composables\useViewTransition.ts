export default function useViewTransition(callback: () => void) {
  function startViewTransition() {
     // @ts-ignore
    if (!document.startViewTransition || window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      callback()
      return
    }
     // @ts-ignore
    return document.startViewTransition(async () => {
      await Promise.resolve(callback())
    })
  }

  return {
    startViewTransition,
  }
}

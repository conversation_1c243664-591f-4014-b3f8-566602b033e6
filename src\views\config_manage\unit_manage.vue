<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import unitManageApi from '@/api/modules/unitManage'
import importOrExportApi from '@/api/modules/importOrExport'
import { extractFilenameFromResponse, saveFileWithPicker } from '@/utils'

defineOptions({
  name: 'UnitManage',
})

// 表格数据
const tableData: Ref<any[]> = ref([])
// 选中的行数据
const selectedRows = ref<any[]>([])

// 分页相关变量
const total = ref(0)
const currentPage = ref(1)
const pageCount = ref(0)
const pageSize = ref(20)

// 加载状态
const loading = ref(false)

// 新增单位弹窗控制
const addDialogVisible = ref(false)
// 编辑单位弹窗控制
const editDialogVisible = ref(false)
// 导入弹窗显示控制
const importDialogVisible = ref(false)

// 新增单位表单数据
const addForm = reactive({
  organization: '',
  unit_name: ''
})

// 编辑单位表单数据
const editForm = reactive({
  id: '',
  originalName: '',
  organization: '',
  unit_name: ''
})

// 导入文件相关
const uploadFile = ref(null)
const fileList = ref([])

// 表单引用
const addFormRef = ref()
const editFormRef = ref()

// 组织架构选项
const organizationOptions = [
  { label: '总部', value: '总部' },
  { label: '分部', value: '分部' },
  { label: '省公司', value: '省公司' },
  { label: '直属单位', value: '直属单位' }
]

// 新增单位表单验证规则
const addRules = reactive({
  organization: [
    { required: true, message: '请选择组织架构类型', trigger: 'change' }
  ],
  unit_name: [
    { required: true, message: '请输入单位名称', trigger: 'blur' },
    { min: 2, message: '单位名称不能少于2个字符', trigger: 'blur' },
    { max: 255, message: '单位名称不能超过255个字符', trigger: 'blur' }
  ]
})

// 编辑单位表单验证规则
const editRules = reactive({
  organization: [
    { required: true, message: '请选择组织架构类型', trigger: 'change' }
  ],
  unit_name: [
    { required: true, message: '请输入单位名称', trigger: 'blur' },
    { min: 2, message: '单位名称不能少于2个字符', trigger: 'blur' },
    { max: 255, message: '单位名称不能超过255个字符', trigger: 'blur' }
  ]
})

// 获取单位列表
const getUnitList = async () => {
  try {
    loading.value = true
    const res: any = await unitManageApi.getUnitList({
      page_flag: 'true',
      page_index: currentPage.value,
      page_size: pageSize.value
    })

    if (res?.code === 200 && res?.res_code === '000000') {
      const data = res.data
      if (data.page_list) {
        // 分页模式
        tableData.value = data.page_list || []
        total.value = data.total_num || 0
        pageCount.value = data.page_num || 0
      } else {
        // 非分页模式
        tableData.value = Array.isArray(data) ? data : []
        total.value = tableData.value.length
        pageCount.value = 1
      }
    } else {
      ElMessage.error(res?.message || '获取单位列表失败')
    }
  } catch (error) {
    console.error('获取单位列表失败:', error)
    ElMessage.error('获取单位列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理页码变化
const handlePageChange = async (val: number) => {
  currentPage.value = val
  await getUnitList()
}

// 处理每页数量变化
const handleSizeChange = async (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  await getUnitList()
}

// 打开导入弹窗
const handleImport = () => {
  importDialogVisible.value = true
  fileList.value = []
}

// 下载单位信息导入模板
const downloadTemplate = async () => {
  try {
    const res = await importOrExportApi.downloadUnitTemplate()
    const filename = extractFilenameFromResponse(res, '单位信息导入模板.xlsx')

    // 调用优化后的saveFileWithPicker
    const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    if (saveResult === true) {
      // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
    } else if (saveResult === 'default') {
      // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
    } else {
      // 用户取消操作
      ElMessage.info('模板下载已取消')
    }
  } catch(error) {
    console.error('下载模板失败:', error)
    ElMessage.error('模板下载失败，请稍后重试')
  }
}

// 下载文件工具方法
const downloadFile = (data: any, fileName: string) => {
  const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = fileName
  link.click()
  URL.revokeObjectURL(link.href)
}

// 文件上传前的验证
const beforeUpload = (file: any) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件(格式为xlsx或xls)')
    return false
  }
  return true
}

// 文件变化处理
const handleFileChange = (file: any, uploadFileList: any) => {
  if (file.status === 'ready') {
    uploadFile.value = file.raw
    fileList.value = uploadFileList
  }
}

// 文件移除处理
const handleRemove = () => {
  uploadFile.value = null
  fileList.value = []
}

// 确认导入
const confirmImport = async () => {
  if (!uploadFile.value || fileList.value.length === 0) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  try {
    const formData = new FormData()
    formData.append('file', uploadFile.value)

    const res: any = await importOrExportApi.importUnitExcel(formData)
    if (res?.code === 200 && res.res_code === '000000') {
      ElMessage.success(res.message || '单位信息导入成功')
      importDialogVisible.value = false
      // 刷新数据
      await getUnitList()
    } else {
      ElMessage.warning(res?.message || res.data || '导入失败，请检查文件格式或查看文件是否有未填项')
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请稍后重试')
  }
}

// 取消导入
const cancelImport = () => {
  importDialogVisible.value = false
  uploadFile.value = null
  fileList.value = []
}

// 导出单位Excel
const handleExport = async () => {
  try {
    const res = await importOrExportApi.exportUnitExcel()
    const filename = extractFilenameFromResponse(res, '单位信息.xlsx')

    // 调用优化后的saveFileWithPicker
    const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    if (saveResult === true) {
      // 自定义路径保存成功
    } else if (saveResult === 'default') {
      // 默认路径下载成功
    } else {
      // 用户取消操作
      ElMessage.info('导出已取消')
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

// 打开新增单位弹窗
const handleAdd = () => {
  addDialogVisible.value = true
  // 重置表单
  addForm.organization = ''
  addForm.unit_name = ''
}

// 打开编辑单位弹窗
const handleEdit = (row: any) => {
  editDialogVisible.value = true
  // 设置编辑数据
  editForm.id = row.id
  editForm.originalName = row.unit_name
  editForm.organization = row.organization
  editForm.unit_name = '' // 修改：不回显原单位名称，让用户重新输入
}

// 新增单位提交
const handleAddSubmit = () => {
  addFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const res: any = await unitManageApi.addUnit({
          unit_info: [{
            organization: addForm.organization,
            unit_name: addForm.unit_name
          }]
        })

        if (res?.code === 200 && res?.res_code === '000000') {
          ElMessage.success(res?.message || '新增单位成功')
          addDialogVisible.value = false
          // 重置表单
          addForm.organization = ''
          addForm.unit_name = ''
          // 刷新列表
          await getUnitList()
        } else {
          ElMessage.error(res?.message || '新增单位失败')
        }
      } catch (error) {
        console.error('新增单位失败:', error)
        ElMessage.error('新增单位失败，请稍后重试')
      }
    }
  })
}

// 新增单位取消
const handleAddCancel = () => {
  addDialogVisible.value = false
  // 重置表单
  addForm.organization = ''
  addForm.unit_name = ''
}

// 编辑单位提交
const handleEditSubmit = () => {
  editFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const res: any = await unitManageApi.updateUnit({
          id: editForm.id,
          organization: editForm.organization,
          unit_name: editForm.unit_name
        })

        if (res?.code === 200 && res?.res_code === '000000') {
          ElMessage.success(res?.message || '编辑单位成功')
          editDialogVisible.value = false
          // 重置表单
          editForm.id = ''
          editForm.originalName = ''
          editForm.organization = ''
          editForm.unit_name = ''
          // 刷新列表
          await getUnitList()
        } else {
          ElMessage.error(res?.message || '编辑单位失败')
        }
      } catch (error) {
        console.error('编辑单位失败:', error)
        ElMessage.error('编辑单位失败，请稍后重试')
      }
    }
  })
}

// 编辑单位取消
const handleEditCancel = () => {
  editDialogVisible.value = false
  // 重置表单
  editForm.id = ''
  editForm.originalName = ''
  editForm.organization = ''
  editForm.unit_name = ''
}

// 表格选择变化处理
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 批量编辑单位
const handleBatchEdit = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要编辑的单位')
    return
  }

  // 执行编辑操作
  handleEdit(selectedRows.value[0])
}

// 批量删除单位
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的单位')
    return
  }
  // const unitNames = selectedRows.value.map((row: any) => row.unit_name).join('、')
  ElMessageBox.alert(
    `是否确认删除以上单位信息?`,
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      // 用户点击确认，执行批量删除操作
      try {
        const ids = selectedRows.value.map((row: any) => row.id)
        const res: any = await unitManageApi.deleteUnit({ ids })

        if (res?.code === 200 && res?.res_code === '000000') {
          ElMessage.success(res?.message || '删除单位成功')
          selectedRows.value = []
          // 刷新列表
          await getUnitList()
        } else {
          ElMessage.error(res?.message || '删除单位失败')
        }
      } catch (error) {
        console.error('删除单位失败:', error)
        // ElMessage.error('删除单位失败，请稍后重试')
      }
    })
    .catch(() => {
      // 用户点击取消，不执行任何操作
      ElMessage.info('已取消删除')
    })
}

// 页面加载时获取单位列表
onMounted(() => {
  getUnitList()
})
</script>

<template>
  <PageMain>
    <div class="flex justify-between mb-4">
      <h2 class="text-lg font-bold">单位名称列表</h2>
      <div class="w-auto flex justify-between items-center">
        <el-button color="#00706B" @click="handleImport" v-auth="['configmanage.unitmanage.import']">导入</el-button>
        <el-button color="#00706B" @click="handleExport" v-auth="['configmanage.unitmanage.export']">导出</el-button>
        <el-button color="#00706B" @click="handleAdd" v-auth="['configmanage.unitmanage.add']">新增</el-button>
        <el-button color="#00706B" @click="handleBatchEdit" v-auth="['configmanage.unitmanage.edit']" :disabled="selectedRows?.length !== 1">编辑</el-button>
        <el-button color="#00706B" @click="handleBatchDelete" v-auth="['configmanage.unitmanage.delete']" :disabled="!selectedRows?.length">删除</el-button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="my-4">
      <el-table
        class="table-height-query"
        :data="tableData"
        border
        size="small"
        v-loading="loading"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column align="center" prop="organization" label="组织架构"></el-table-column>
        <el-table-column align="center" prop="unit_name" label="单位名称"></el-table-column>
        <!-- <el-table-column align="center" label="操作" fixed="right" width="120">
          <template #default="scope">
            <div class="flex justify-center flex-wrap">
              <el-button size="small" color="#00706B" @click="handleEdit(scope.row)" v-auth="['configmanage.unitmanage.edit']">编辑</el-button>
            </div>
          </template>
        </el-table-column> -->
      </el-table>
    </div>

    <!-- 翻页组件 -->
    <div class="flex justify-end">
      <el-pagination
        background
        layout="sizes, total, prev, pager, next"
        :page-size="pageSize"
        :page-sizes="[20, 30, 40, 50, 100]"
        :total="total"
        :page-count="pageCount"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      ></el-pagination>
    </div>

    <!-- 新增单位弹窗 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增单位信息"
      width="420"
      center
      destroy-on-close>
      <el-form
        :model="addForm"
        :rules="addRules"
        ref="addFormRef"
        label-position="left"
        label-width="100px">
        <el-form-item label="组织架构:" prop="organization">
          <el-select
            v-model="addForm.organization"
            placeholder="请选择组织架构类型"
            style="width: 100%;">
            <el-option
              v-for="item in organizationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位名称:" prop="unit_name">
          <el-input
            v-model="addForm.unit_name"
            placeholder="请输入单位名称"
            style="width: 100%;">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="handleAddSubmit">保存</el-button>
          <el-button @click="handleAddCancel">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑单位弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑单位信息"
      width="420"
      center
      destroy-on-close>
      <div class="original-unit-display">
        <span class="original-unit-label">原单位名称：</span>
        <span class="original-unit-value">{{ editForm.originalName }}</span>
      </div>
      <el-form
        :model="editForm"
        :rules="editRules"
        ref="editFormRef"
        label-position="left"
        label-width="100px">
        <el-form-item label="组织架构:" prop="organization">
          <el-select
            v-model="editForm.organization"
            placeholder="请选择组织架构类型"
            style="width: 100%;">
            <el-option
              v-for="item in organizationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位名称:" prop="unit_name">
          <el-input
            v-model="editForm.unit_name"
            placeholder="请输入单位名称"
            style="width: 100%;">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="handleEditSubmit">保存</el-button>
          <el-button @click="handleEditCancel">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入单位弹窗 -->
    <el-dialog v-model="importDialogVisible" width="500" center align-center destroy-on-close>
      <div class="mb-4 flex flex-col items-center">
        <div class="flex items-center mb-4">
          <span class="mr-2">导入模板:</span>
          <el-button type="text" @click="downloadTemplate">点击下载单位信息导入模板</el-button>
        </div>

        <div class="flex items-center">
          <span class="mr-2">导入文件:</span>
          <div>
            <div v-if="fileList.length > 0" class="mb-2 text-orange-500 text-xs">
              如需更换文件，请先点击文件右侧的"×"删除当前文件
            </div>
            <el-upload
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :limit="1"
              :file-list="fileList"
              :on-change="handleFileChange"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              accept=".xlsx,.xls"
            >
              <el-button color="#00706B" :disabled="fileList.length > 0">文件上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传xlsx/xls文件
                </div>
              </template>
            </el-upload>
          </div>
        </div>
      </div>
      <template #header="{ titleId }">
        <div :id="titleId" class="flex font-600 mb-6">导入</div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="confirmImport">确认</el-button>
          <el-button @click="cancelImport">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </PageMain>
</template>

<style scoped>
/* 自定义样式 */
.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 原单位名称显示样式 */
.original-unit-display {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.original-unit-label {
  width: 110px; /* 与表单标签宽度一致 */
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.original-unit-value {
  flex: 1;
  font-size: 14px;
  color: #909399;
}

/* 确保表单标签对齐 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  white-space: nowrap; /* 防止标签文本换行 */
}

/* 必填项星号颜色 */
:deep(.el-form-item.is-required .el-form-item__label::before) {
  color: #f56c6c;
}

/* 确保弹窗标题在左上角 */
:deep(.el-dialog__header) {
  text-align: left;
}

:deep(.el-dialog__title) {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
  color: #303133;
}

:deep(.el-dialog__body) {
  padding: 20px 20px 10px;
}
</style>

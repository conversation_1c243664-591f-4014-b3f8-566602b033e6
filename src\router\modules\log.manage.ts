import type { RouteRecordRaw } from 'vue-router'
import { $t } from '@/locales'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/log',
  component: Layout,
  redirect: '/log/logmanage',
  name: 'Log',
  meta: {
    title: $t('route.log.manage'),
    icon: 'ep:document',
    defaultOpened : true,
    breadcrumb: false,
    auth: ['logmanage']
  },
  children: [
    {
      path: 'logmanage',
      name: 'logManage',
      component: () => import('@/views/log_manage/log_manage_page.vue'),
      meta: {
        title: $t('route.log.manage'),
        menu: false,
        activeMenu: '/log'
      },
    },
  ],
}

export default routes

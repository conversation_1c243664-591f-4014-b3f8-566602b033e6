import api from '../index'

export default {
  // 查询网站的历史报告
  queryHistoryReports: (params: {
    page_index?: number,
    page_size?: number,
    unit_name?: string,
    start_date?: string,
    end_date?: string
  }) => api.get('/report', {
    params: {
      ...params,
    },
  }),

  // 查看 & 下载报告
  downloadHistoryReport: (reportId: string) => api.get(`/report/${reportId}`, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/octet-stream'
    },
  }),

  // 批量下载报告
  patchDownloadHistoryReport: (data: { ids: string[] }) => api.post('download/reports', data, {
    responseType: 'blob',
  })
}

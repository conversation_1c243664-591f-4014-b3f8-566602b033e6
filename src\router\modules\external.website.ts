import type { RouteRecordRaw } from 'vue-router'
import { $t } from '@/locales'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/executive',
  component: Layout,
  redirect: '/executive/list',
  name: 'executive',
  meta: {
    title: $t('route.external.root'),
    icon: 'ep:link',
    defaultOpened : true,
    breadcrumb: false,
    // auth: ['SYSTEM_ADMIN']
  },
  children: [
    {
      path: 'list',
      name: 'executiveList',
      component: () => import('@/views/external/executive_list.vue'),
      meta: {
        title: $t('route.external.root'),
        menu: false,
      },
    },
    {
      path: 'websitedetail',
      name: 'websiteDetail',
      component: () => import('@/views/check_config_management/website_detail.vue'),
      meta: {
        title: $t('route.checkconfig.websiteDetail'),
        menu: false,
        activeMenu: '/executive/list',
      },
    },
    {
      path: 'effective',
      name: 'effective',
      component: () => import('@/views/external/effective.vue'),
      meta: {
        title: $t('route.external.effective'),
        menu: false,
        activeMenu: '/executive/list',
      },
    },
  ],
}

export default routes

<i18n>
{
  "zh-cn": {
    "intro": "探索从这里开始! 🚀",
    "hasAccount": "已经有帐号?",
    "login": "去登录",
    "form": {
      "account": "用户名",
      "captcha": "验证码",
      "sendCaptcha": "发送验证码",
      "password": "密码",
      "checkPassword": "确认密码",
      "register": "注册"
    },
    "rules": {
      "account": "请输入用户名",
      "captcha": "请输入验证码",
      "password": "请输入密码",
      "passwordLength": "密码长度为6到18位",
      "checkPassword": "请再次输入密码",
      "checkPasswordError": "两次输入的密码不一致"
    }
  },
  "zh-tw": {
    "intro": "探索從這裡開始! 🚀",
    "hasAccount": "已有帳號?",
    "login": "去登入",
    "form": {
      "account": "用戶名",
      "captcha": "驗證碼",
      "sendCaptcha": "發送驗證碼",
      "password": "密碼",
      "checkPassword": "確認密碼",
      "register": "註冊"
    },
    "rules": {
      "account": "請輸入用戶名",
      "captcha": "請輸入驗證碼",
      "password": "請輸入密碼",
      "passwordLength": "密碼長度為6到18位",
      "checkPassword": "請再次輸入密碼",
      "checkPasswordError": "兩次輸入的密碼不一致"
    }
  },
  "en": {
    "intro": "Explore from here! 🚀",
    "hasAccount": "Already have an account?",
    "login": "Login",
    "form": {
      "account": "Account",
      "captcha": "Captcha",
      "sendCaptcha": "Send Captcha",
      "password": "Password",
      "checkPassword": "Check Password",
      "register": "Register"
    },
    "rules": {
      "account": "Please enter an account",
      "captcha": "Please enter a captcha",
      "password": "Please enter a password",
      "passwordLength": "Password length is 6 to 18 bits",
      "checkPassword": "Please enter the password again",
      "checkPasswordError": "The two passwords entered are inconsistent"
    }
  }
}
</i18n>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

defineOptions({
  name: 'RegisterForm',
})

const props = defineProps<{
  account?: string
}>()

const emits = defineEmits<{
  onLogin: [account: string]
  onRegister: [account: string]
}>()

const { t } = useI18n()

const loading = ref(false)

const formRef = ref<FormInstance>()
const form = ref({
  account: props.account ?? '',
  captcha: '',
  password: '',
  checkPassword: '',
})
const rules = ref<FormRules>({
  account: [
    { required: true, trigger: 'blur', message: () => t('rules.account') },
  ],
  captcha: [
    { required: true, trigger: 'blur', message: () => t('rules.captcha') },
  ],
  password: [
    { required: true, trigger: 'blur', message: () => t('rules.password') },
    { min: 6, max: 18, trigger: 'blur', message: () => t('rules.passwordLength') },
  ],
  checkPassword: [
    { required: true, trigger: 'blur', message: () => t('rules.checkPassword') },
    {
      validator: (rule, value, callback) => {
        if (value !== form.value.password) {
          callback(new Error(t('rules.checkPasswordError')))
        }
        else {
          callback()
        }
      },
    },
  ],
})
function handleRegister() {
  ElMessage({
    message: '注册模块仅提供界面演示，无实际功能，需开发者自行扩展',
    type: 'warning',
  })
  formRef.value?.validate((valid) => {
    if (valid) {
      // 这里编写业务代码
      emits('onRegister', form.value.account)
    }
  })
}
</script>

<template>
  <ElForm ref="formRef" :model="form" :rules="rules" class="min-h-500px w-full flex-col-stretch-center p-12">
    <h3 class="mb-8 text-xl color-[var(--el-text-color-primary)] font-bold">
      {{ t('intro') }}
    </h3>
    <div>
      <ElFormItem prop="account">
        <ElInput v-model="form.account" size="large" :placeholder="t('form.account')" tabindex="1">
          <template #prefix>
            <SvgIcon name="i-ri:user-3-fill" />
          </template>
        </ElInput>
      </ElFormItem>
      <ElFormItem prop="captcha">
        <ElInput v-model="form.captcha" size="large" :placeholder="t('form.captcha')" tabindex="2">
          <template #prefix>
            <SvgIcon name="i-ic:baseline-verified-user" />
          </template>
          <template #append>
            <ElButton>{{ t('form.sendCaptcha') }}</ElButton>
          </template>
        </ElInput>
      </ElFormItem>
      <ElFormItem prop="password">
        <ElInput v-model="form.password" type="password" size="large" :placeholder="t('form.password')" tabindex="3" show-password>
          <template #prefix>
            <SvgIcon name="i-ri:lock-2-fill" />
          </template>
        </ElInput>
      </ElFormItem>
      <ElFormItem prop="checkPassword">
        <ElInput v-model="form.checkPassword" type="password" size="large" :placeholder="t('form.checkPassword')" tabindex="4" show-password>
          <template #prefix>
            <SvgIcon name="i-ri:lock-2-fill" />
          </template>
        </ElInput>
      </ElFormItem>
    </div>
    <ElButton :loading="loading" type="primary" size="large" style="width: 100%; margin-top: 20px;" @click.prevent="handleRegister">
      {{ t('form.register') }}
    </ElButton>
    <div class="mt-4 flex-center gap-2 text-sm color-[var(--el-text-color-secondary)]">
      {{ t('hasAccount') }}
      <ElLink type="primary" :underline="false" @click="emits('onLogin', form.account)">
        {{ t('login') }}
      </ElLink>
    </div>
  </ElForm>
</template>

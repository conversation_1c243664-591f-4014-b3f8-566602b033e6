import type { RouteRecordRaw } from 'vue-router'
import { $t } from '@/locales'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/checkconfigmanagement',
  component: Layout,
  redirect: '/checkconfigmanagement/websitecolumnmanagement',
  name: 'checkConfigManagement',
  meta: {
    title: $t('route.checkconfig.root'),
    icon: 'ep:home-filled',
    breadcrumb: true,
    defaultOpened : true,
    activeMenu: '/checkconfigmanagement/websitecolumnmanagement',
    auth: ['websitemanage']
  },
  children: [
    {
      path: 'websitecolumnmanagement',
      name: 'websiteColumnManagement',
      component: () => import('@/views/check_config_management/website_column_management.vue'),
      meta: {
        title: $t('route.checkconfig.root'),
        menu: false,
        breadcrumb: false,
        activeMenu: '/checkconfigmanagement',
      },
    },
    /* {
      path: 'websitedetail',
      name: 'websiteDetail',
      component: () => import('@/views/check_config_management/website_detail.vue'),
      meta: {
        title: $t('route.checkconfig.websiteDetail'),
        menu: false,
        activeMenu: '/checkconfigmanagement/websitecolumnmanagement',
      },
    }, */
    {
      path: 'websiteedit',
      name: 'websiteEdit',
      component: () => import('@/views/check_config_management/website_edit.vue'),
      meta: {
        title: $t('route.checkconfig.websiteDetail'),
        menu: false,
        activeMenu: '/checkconfigmanagement',
      },
    },
    {
      path: 'validtyruleconfig',
      name: 'validtyRuleConfig',
      component: () => import('@/views/check_config_management/validity_rule_config.vue'),
      meta: {
        title: $t('route.checkconfig.validityRuleConfig'),
        menu: false,
        activeMenu: '/checkconfigmanagement',
      },
      children: [
        {
          path: 'website',
          name: 'website',
          component: () => import('@/views/external/website_list.vue'),
          meta: {
            title: $t('route.external.website'),
            menu: false,
            activeMenu: '/checkconfigmanagement',
          },
        }
      ],
    },
  ],
}

export default routes

import type { RouteRecordRaw } from 'vue-router'
import { $t } from '@/locales'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/config',
  component: Layout,
  redirect: '/config/unitmanage',
  name: 'config',
  meta: {
    title: $t('route.config.root'),
    icon: 'ep:setting',
    defaultOpened : true,
    breadcrumb: false,
    auth: ['configmanage']
  },
  children: [
    {
      path: 'unitmanage',
      name: 'unitManage',
      component: () => import('@/views/config_manage/unit_manage.vue'),
      meta: {
        menu: true,
        // activeMenu: '/config/unitmanage',
        title: $t('route.config.unitmanage'),
        auth: ['configmanage.unitmanage']
      },
    },

  ],
}

export default routes

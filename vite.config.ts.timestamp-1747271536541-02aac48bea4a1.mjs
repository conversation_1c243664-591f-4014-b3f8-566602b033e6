// vite.config.ts
import fs2 from "node:fs";
import path2 from "node:path";
import process2 from "node:process";
import { defineConfig, loadEnv } from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/vite@5.4.3_@types+node@22.5.4_sass@1.78.0_terser@5.32.0/node_modules/vite/dist/node/index.js";
import dayjs2 from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js";

// package.json
var package_default = {
  type: "module",
  version: "4.11.0",
  engines: {
    node: "^18.18.0 || ^20.9.0 || >=21.1.0"
  },
  scripts: {
    dev: "vite",
    build: "vue-tsc && vite build",
    "build:test": "vue-tsc && vite build --mode test",
    serve: "http-server ./sgcc_front -o",
    "serve:test": "http-server ./sgcc_front-test -o",
    svgo: "svgo -f src/assets/icons",
    new: "plop",
    "generate:icons": "esno ./scripts/generate.icons.ts",
    lint: "npm-run-all -s lint:tsc lint:eslint lint:stylelint",
    "lint:tsc": "vue-tsc",
    "lint:eslint": "eslint . --cache --fix",
    "lint:stylelint": 'stylelint "src/**/*.{css,scss,vue}" --cache --fix',
    postinstall: "simple-git-hooks",
    preinstall: "npx only-allow pnpm",
    commit: "git cz",
    release: "bumpp"
  },
  dependencies: {
    "@headlessui/vue": "^1.7.22",
    "@imengyu/vue3-context-menu": "^1.4.2",
    "@types/crypto-js": "^4.2.2",
    "@vueuse/components": "^11.0.3",
    "@vueuse/core": "^11.0.3",
    "@vueuse/integrations": "^11.0.3",
    axios: "^1.7.7",
    "crypto-js": "^4.2.0",
    dayjs: "^1.11.13",
    defu: "^6.1.4",
    "disable-devtool": "^0.3.7",
    "docx-preview": "^0.3.2",
    "element-plus": "^2.8.1",
    eruda: "^3.2.3",
    "floating-vue": "5.2.2",
    "hotkeys-js": "^3.13.7",
    "lodash-es": "^4.17.21",
    "medium-zoom": "^1.1.0",
    mitt: "^3.0.1",
    mockjs: "^1.1.0",
    nprogress: "^0.2.0",
    overlayscrollbars: "^2.10.0",
    "overlayscrollbars-vue": "^0.5.9",
    "path-browserify": "^1.0.1",
    "path-to-regexp": "^7.1.0",
    pinia: "^2.2.2",
    "pinyin-pro": "^3.24.2",
    qs: "^6.13.0",
    scule: "^1.3.0",
    sortablejs: "^1.15.2",
    spinkit: "^2.0.1",
    "timeago.js": "^4.0.2",
    "ts-md5": "^1.3.1",
    "v-wave": "^2.0.0",
    vconsole: "^3.15.1",
    vue: "^3.4.38",
    "vue-i18n": "^9.14.0",
    "vue-m-message": "^4.0.2",
    "vue-router": "^4.4.3",
    "vxe-table": "^4.7.80",
    "watermark-js-plus": "^1.5.5"
  },
  devDependencies: {
    "@antfu/eslint-config": "2.24.1",
    "@iconify/json": "^2.2.243",
    "@iconify/vue": "^4.1.2",
    "@intlify/unplugin-vue-i18n": "^4.0.0",
    "@stylistic/stylelint-config": "^2.0.0",
    "@types/lodash-es": "^4.17.12",
    "@types/mockjs": "^1.0.10",
    "@types/nprogress": "^0.2.3",
    "@types/path-browserify": "^1.0.3",
    "@types/qs": "^6.9.15",
    "@types/sortablejs": "^1.15.8",
    "@unocss/eslint-plugin": "^0.62.3",
    "@vitejs/plugin-legacy": "^5.4.2",
    "@vitejs/plugin-vue": "^5.1.3",
    "@vitejs/plugin-vue-jsx": "^4.0.1",
    archiver: "^7.0.1",
    autoprefixer: "^10.4.20",
    boxen: "^8.0.1",
    bumpp: "^9.5.2",
    "cz-git": "^1.9.4",
    eslint: "^9.9.1",
    esno: "^4.7.0",
    "fs-extra": "^11.2.0",
    "http-server": "^14.1.1",
    inquirer: "^10.1.8",
    "lint-staged": "^15.2.9",
    "npm-run-all2": "^6.2.2",
    picocolors: "^1.0.1",
    plop: "^4.0.1",
    postcss: "^8.4.42",
    "postcss-nested": "^6.2.0",
    sass: "^1.77.8",
    "simple-git-hooks": "^2.11.1",
    stylelint: "^16.9.0",
    "stylelint-config-recess-order": "^5.1.0",
    "stylelint-config-standard-scss": "^13.1.0",
    "stylelint-config-standard-vue": "^1.0.0",
    "stylelint-scss": "^6.5.1",
    svgo: "^3.3.2",
    typescript: "^5.5.4",
    unocss: "^0.62.3",
    "unocss-preset-scrollbar": "^0.3.1",
    "unplugin-auto-import": "^0.18.2",
    "unplugin-turbo-console": "^1.10.1",
    "unplugin-vue-components": "^0.27.4",
    vite: "^5.4.2",
    "vite-plugin-banner": "^0.7.1",
    "vite-plugin-compression2": "^1.2.0",
    "vite-plugin-fake-server": "^2.1.1",
    "vite-plugin-pages": "^0.32.3",
    "vite-plugin-svg-icons": "^2.0.1",
    "vite-plugin-vue-devtools": "^7.3.9",
    "vite-plugin-vue-meta-layouts": "^0.4.3",
    "vue-tsc": "^2.1.4"
  },
  "simple-git-hooks": {
    "pre-commit": "pnpm lint-staged",
    preserveUnused: true
  },
  config: {
    commitizen: {
      path: "node_modules/cz-git"
    }
  }
};

// vite/plugins.ts
import path from "node:path";
import process from "node:process";
import fs from "node:fs";
import dayjs from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js";
import vue from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/@vitejs+plugin-vue@5.1.3_vite@5.4.3_@types+node@22.5.4_sass@1.78.0_terser@5.32.0__vue@3.5.3_typescript@5.6.2_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0.1_vite@5.4.3_@types+node@22.5.4_sass@1.78.0_terser@5.32.0__vue@3.5.3_typescript@5.6.2_/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import vueLegacy from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/@vitejs+plugin-legacy@5.4.2_terser@5.32.0_vite@5.4.3_@types+node@22.5.4_sass@1.78.0_terser@5.32.0_/node_modules/@vitejs/plugin-legacy/dist/index.mjs";
import VueDevTools from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/vite-plugin-vue-devtools@7.4.4_@nuxt+kit@3.13.1_rollup@4.21.2__rollup@4.21.2_vite@5.4.3_@type_d2xcdhwp4stpgc7fybfx3rdw3e/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import autoImport from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/unplugin-auto-import@0.18.2_@nuxt+kit@3.13.1_rollup@4.21.2__@vueuse+core@11.0.3_vue@3.5.3_typ_xco6myctzkg6bcuztf2fo4jocq/node_modules/unplugin-auto-import/dist/vite.js";
import components from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/unplugin-vue-components@0.27.4_@babel+parser@7.25.6_@nuxt+kit@3.13.1_rollup@4.21.2__rollup@4._hg6sotnkazp6ro3kifxymimbay/node_modules/unplugin-vue-components/dist/vite.js";
import Unocss from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/unocss@0.62.3_postcss@8.4.45_rollup@4.21.2_vite@5.4.3_@types+node@22.5.4_sass@1.78.0_terser@5.32.0_/node_modules/unocss/dist/vite.mjs";
import { createSvgIconsPlugin } from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_vite@5.4.3_@types+node@22.5.4_sass@1.78.0_terser@5.32.0_/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import { vitePluginFakeServer } from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/vite-plugin-fake-server@2.1.2/node_modules/vite-plugin-fake-server/dist/index.mjs";
import vueI18n from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/@intlify+unplugin-vue-i18n@4.0.0_rollup@4.21.2_vue-i18n@9.14.0_vue@3.5.3_typescript@5.6.2__/node_modules/@intlify/unplugin-vue-i18n/lib/vite.mjs";
import Layouts from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/vite-plugin-vue-meta-layouts@0.4.3_vite@5.4.3_@types+node@22.5.4_sass@1.78.0_terser@5.32.0__v_gy2bq2ou2xe7nopburvygacd5y/node_modules/vite-plugin-vue-meta-layouts/dist/index.mjs";
import Pages from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/vite-plugin-pages@0.32.3_@vue+compiler-sfc@3.5.3_vite@5.4.3_@types+node@22.5.4_sass@1.78.0_te_ooaynps4clqbbywixwjn6d4zgq/node_modules/vite-plugin-pages/dist/index.js";
import { compression } from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/vite-plugin-compression2@1.3.0_rollup@4.21.2/node_modules/vite-plugin-compression2/dist/index.mjs";
import archiver from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/archiver@7.0.1/node_modules/archiver/index.js";
import TurboConsole from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/unplugin-turbo-console@1.10.1_@babel+parser@7.25.6_@nuxt+kit@3.13.1_rollup@4.21.2__@nuxt+sche_ou7bzdgdm3shevfd5tv5txy22q/node_modules/unplugin-turbo-console/dist/vite.mjs";
import banner from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/vite-plugin-banner@0.7.1/node_modules/vite-plugin-banner/dist/index.mjs";
import boxen from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/boxen@8.0.1/node_modules/boxen/index.js";
import picocolors from "file:///D:/_WORK_SPACE/website-monitor/node_modules/.pnpm/picocolors@1.1.0/node_modules/picocolors/picocolors.js";
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [
    vue(),
    vueJsx(),
    vueLegacy({
      renderLegacyChunks: false,
      modernPolyfills: [
        "es.array.at",
        "es.array.find-last"
      ]
    }),
    // https://github.com/vuejs/devtools-next
    viteEnv.VITE_OPEN_DEVTOOLS === "true" && VueDevTools(),
    // https://github.com/unplugin/unplugin-auto-import
    autoImport({
      imports: [
        "vue",
        "vue-router",
        "pinia"
      ],
      dts: "./src/types/auto-imports.d.ts",
      dirs: [
        "./src/utils/composables/**"
      ]
    }),
    // https://github.com/unplugin/unplugin-vue-components
    components({
      dirs: [
        "src/components",
        "src/layouts/ui-kit"
      ],
      include: [/\.vue$/, /\.vue\?vue/, /\.tsx$/],
      dts: "./src/types/components.d.ts"
    }),
    Unocss(),
    // https://github.com/vbenjs/vite-plugin-svg-icons
    createSvgIconsPlugin({
      iconDirs: [path.resolve(process.cwd(), "src/assets/icons/")],
      symbolId: "icon-[dir]-[name]",
      svgoOptions: isBuild
    }),
    // https://github.com/condorheroblog/vite-plugin-fake-server
    vitePluginFakeServer({
      logger: !isBuild,
      include: "src/mock",
      infixName: false,
      enableProd: isBuild && viteEnv.VITE_BUILD_MOCK === "true"
    }),
    // https://github.com/intlify/vue-i18n
    vueI18n({
      include: path.resolve(process.cwd(), "src/locales/lang/**")
    }),
    // https://github.com/dishait/vite-plugin-vue-meta-layouts
    Layouts({
      defaultLayout: "index"
    }),
    // https://github.com/hannoeru/vite-plugin-pages
    Pages({
      dirs: "src/views",
      exclude: [
        "**/components/**/*.vue"
      ]
    }),
    // https://github.com/nonzzz/vite-plugin-compression
    isBuild && viteEnv.VITE_BUILD_COMPRESS.split(",").includes("gzip") && compression(),
    isBuild && viteEnv.VITE_BUILD_COMPRESS.split(",").includes("brotli") && compression({
      exclude: [/\.(br)$/, /\.(gz)$/],
      algorithm: "brotliCompress"
    }),
    /* @__PURE__ */ function() {
      let outDir;
      return {
        name: "vite-plugin-archiver",
        apply: "build",
        configResolved(resolvedConfig) {
          outDir = resolvedConfig.build.outDir;
        },
        async closeBundle() {
          if (["zip", "tar"].includes(viteEnv.VITE_BUILD_ARCHIVE)) {
            await sleep(1e3);
            const archive = archiver(viteEnv.VITE_BUILD_ARCHIVE, {
              ...viteEnv.VITE_BUILD_ARCHIVE === "zip" && { zlib: { level: 9 } },
              ...viteEnv.VITE_BUILD_ARCHIVE === "tar" && { gzip: true, gzipOptions: { level: 9 } }
            });
            const output = fs.createWriteStream(`${outDir}.${dayjs().format("YYYY-MM-DD-HH-mm-ss")}.${viteEnv.VITE_BUILD_ARCHIVE === "zip" ? "zip" : "tar.gz"}`);
            archive.pipe(output);
            archive.directory(outDir, false);
            archive.finalize();
          }
        }
      };
    }(),
    function() {
      const virtualModuleId = "virtual:app-loading";
      const resolvedVirtualModuleId = `\0${virtualModuleId}`;
      return {
        name: "vite-plugin-loading",
        resolveId(id) {
          if (id === virtualModuleId) {
            return resolvedVirtualModuleId;
          }
        },
        load(id) {
          if (id === resolvedVirtualModuleId) {
            return {
              code: `
                export function loadingFadeOut() {
                  const loadingEl = document.querySelector('[data-app-loading]')
                  if (loadingEl) {
                    loadingEl.style['pointer-events'] = 'none'
                    loadingEl.style.visibility = 'hidden'
                    loadingEl.style.opacity = 0
                    loadingEl.style.transition = 'all 0.5s ease-out'
                    loadingEl.addEventListener('transitionend', () => loadingEl.remove(), { once: true })
                  }
                }
              `,
              map: null
            };
          }
        },
        enforce: "pre",
        transformIndexHtml: {
          handler: async (html) => html.replace(/<\/body>/, `${`<div data-app-loading>${await fs.readFileSync(path.resolve(process.cwd(), "loading.html"), "utf8")}</div>`}</body>`),
          order: "pre"
        }
      };
    }(),
    // https://github.com/unplugin/unplugin-turbo-console
    TurboConsole(),
    // https://github.com/chengpeiquan/vite-plugin-banner
    banner(`
/**
 * \u7531 Fantastic-admin \u63D0\u4F9B\u6280\u672F\u652F\u6301
 * Powered by Fantastic-admin
 * https://fantastic-admin.hurui.me
 */
    `),
    {
      name: "vite-plugin-debug-plugin",
      enforce: "pre",
      transform: (code, id) => {
        if (/src\/main.ts$/.test(id)) {
          if (viteEnv.VITE_APP_DEBUG_TOOL === "eruda") {
            code = code.concat(`
              import eruda from 'eruda'
              eruda.init()
            `);
          } else if (viteEnv.VITE_APP_DEBUG_TOOL === "vconsole") {
            code = code.concat(`
              import VConsole from 'vconsole'
              new VConsole()
            `);
          }
          return {
            code,
            map: null
          };
        }
      }
    },
    {
      name: "vite-plugin-disable-devtool",
      enforce: "pre",
      transform: (code, id) => {
        if (/src\/main.ts$/.test(id)) {
          if (viteEnv.VITE_APP_DISABLE_DEVTOOL === "true") {
            code = code.concat(`
              import DisableDevtool from 'disable-devtool'
              DisableDevtool()
            `);
          }
          return {
            code,
            map: null
          };
        }
      }
    },
    {
      name: "vite-plugin-terminal-info",
      apply: "serve",
      async buildStart() {
        const { bold, green, magenta, bgGreen, underline } = picocolors;
        console.log(
          boxen(
            `${bold(green(`\u7531 ${bgGreen("Fantastic-admin")} \u9A71\u52A8`))}

${underline("https://fantastic-admin.hurui.me")}

\u5F53\u524D\u4F7F\u7528\uFF1A${magenta("\u4E13\u4E1A\u7248")}`,
            {
              padding: 1,
              margin: 1,
              borderStyle: "double",
              textAlignment: "center"
            }
          )
        );
      }
    }
  ];
  return vitePlugins;
}

// vite.config.ts
var __vite_injected_original_dirname = "D:\\_WORK_SPACE\\website-monitor";
var vite_config_default = async ({ mode, command }) => {
  const env = loadEnv(mode, process2.cwd());
  const scssResources = [];
  fs2.readdirSync("src/assets/styles/resources").forEach((dirname) => {
    if (fs2.statSync(`src/assets/styles/resources/${dirname}`).isFile()) {
      scssResources.push(`@use "src/assets/styles/resources/${dirname}" as *;`);
    }
  });
  return defineConfig({
    // 开发服务器选项 https://cn.vitejs.dev/config/server-options
    server: {
      open: true,
      host: "0.0.0.0",
      port: 9e3,
      proxy: {
        "/proxy": {
          target: env.VITE_APP_API_BASEURL,
          secure: false,
          changeOrigin: command === "serve" && env.VITE_OPEN_PROXY === "true",
          rewrite: (path3) => path3.replace(/\/proxy/, "")
        }
      }
    },
    // 构建选项 https://cn.vitejs.dev/config/build-options
    build: {
      outDir: mode === "production" ? "sgcc_front" : `sgcc_front-${mode}`,
      sourcemap: env.VITE_BUILD_SOURCEMAP === "true"
    },
    define: {
      __SYSTEM_INFO__: JSON.stringify({
        pkg: {
          version: package_default.version,
          dependencies: package_default.dependencies,
          devDependencies: package_default.devDependencies
        },
        lastBuildTime: dayjs2().format("YYYY-MM-DD HH:mm:ss")
      })
    },
    plugins: createVitePlugins(env, command === "build"),
    base: "./",
    resolve: {
      alias: {
        "@": path2.resolve(__vite_injected_original_dirname, "src"),
        "#": path2.resolve(__vite_injected_original_dirname, "src/types")
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: scssResources.join("")
        }
      }
    }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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

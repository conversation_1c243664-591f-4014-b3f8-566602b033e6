<route lang="yaml">
  meta:
    title: 有效性规则配置
  </route>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import websiteManageApi from '@/api/modules/websiteManagement'

const router = useRouter()
const route = useRoute()

const tableData = ref([]);

// 选中的行数据
const selectedRows = ref<any[]>([])

const { queryWebsiteEffectRule, updateWebsiteEffectRule, executeWebsiteEffectRule } = websiteManageApi

  // Dialog 对话框
const dialogVisible = ref(false);
const formLabelWidth = '120px';
const formRef = ref();
const form = reactive({
  belonging_unit: '',
  network_name: '',
  network_ip: '',
  network_type: '',
  check_level: '',
  check_schedule: '',
  time: '天',
  frequency: 1
});

const network_id = ref('');

// 网页数据总条目数
const total = ref(100);
// 当前页码
const currentPage = ref(1);
// 总页码数量
const pageCount = ref(0)
// 每页显示的数据条目数
const pageSize = ref(20)

// 表单验证规则
const rules = reactive({
  check_level: [{ required: true, message: '请选择检查层级', trigger: 'change' }]
});

onMounted(async () => {
  await getWebsiteData(currentPage.value, pageSize.value, router.currentRoute.value.query.websiteId as string)
})


const getWebsiteData = async (page_index: number, page_size: number, id?: string) => {
  const res = await queryWebsiteEffectRule({ page_index, page_size, id })
  tableData.value = res?.data
  total.value = res?.data?.length
  pageCount.value = res?.data?.page_num
}

const handlePageChange = async (val: number) => {
  currentPage.value = val;
  console.log(`当前页: ${val}`);
  await getWebsiteData(currentPage.value, pageSize.value, router.currentRoute.value.query.websiteId as string)
};

const handleSizeChange = async (val: number) => {
  pageSize.value = val;
  console.log(`每页条数: ${val}`);
  await getWebsiteData(currentPage.value, pageSize.value, router.currentRoute.value.query.websiteId as string)
};

function handleSubmit() {
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      const res: any = await updateWebsiteEffectRule({ check_level: form.check_level, check_schedule: `每${form.time}${form.frequency}次`, network_id: network_id.value })
      if (res.code === 200 && res.res_code === '000000') {
        currentPage.value = 1
        pageSize.value = 20
        await getWebsiteData(currentPage.value, pageSize.value, router.currentRoute.value.query.websiteId as string)
        ElMessage.success('保存成功');
        dialogVisible.value = false;
        return
      } else {
        ElMessage.error(res.message || '保存失败');
        dialogVisible.value = false;
        return
      }
    } else {
      ElMessage.error('表单验证失败');
    }
  });
};

function handleEdit(index: number, row: any) {
  dialogVisible.value = true;
  network_id.value = row.id;
  form.belonging_unit = row.belonging_unit;
  form.network_name = row.network_name;
  form.network_ip = row.network_ip;
  form.network_type = row.network_type;
  form.check_level = row.check_level;
  form.check_schedule = row.check_schedule;
  form.time = row.check_schedule[1]
  form.frequency = row.check_schedule[2] * 1
}

async function handleExecute(index: number, row: any) {
  const res: any = await executeWebsiteEffectRule(row.id)
  console.log('执行有效性规则', res)
  if (res.code === 200 && res.res_code === '000000') {
    ElMessage.success(res.data || '执行成功')
  } else {
    ElMessage.warning(res.data || '执行失败')
  }
}

function handleSrc(index: number, row: any) {
  router.push({
    name: 'website',
    query: {
      websiteId: row.id,
      networkName: row.network_name
    }
  })
}

const handleCancelEdit = () => {
  router.replace({ name: 'checkConfigManagement' })
}

// 表格选择变化处理
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 批量编辑有效性规则
const handleBatchEdit = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要编辑的有效性规则')
    return
  }

  // 执行编辑操作
  const index = tableData.value.findIndex((item: any) => item.id === selectedRows.value[0].id)
  handleEdit(index, selectedRows.value[0])
}
</script>

  <template>
    <div>
      <PageMain>
        <div class="flex justify-between">
          <div class="font-600">
           {{ route.query.networkName }}网站有效性规则列表如下:
          </div>
          <div class="flex">
            <el-button color="#00706B" @click="handleBatchEdit" v-auth="['websitemanage.validity.edit']" :disabled="selectedRows?.length !== 1">编辑</el-button>
            <el-button color="#00706B" @click="handleCancelEdit">返回</el-button>
          </div>
        </div>

        <div class="my-4">
          <el-table :data="tableData" border size="small" class="table-height-query" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column align="center" type="index" label="序号" fixed="left" width="50"></el-table-column>
            <!-- <el-table-column align="center" prop="id" label="ID" fixed="left" width="50" show-overflow-tooltip></el-table-column> -->
            <el-table-column align="center" prop="belonging_unit" label="归属单位"></el-table-column>
            <el-table-column align="center" prop="network_name" label="网站名称" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="network_ip" label="域名/IP" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="network_type" label="网络类型"></el-table-column>
            <el-table-column align="center" prop="check_level" label="检查层级"></el-table-column>
            <el-table-column align="center" prop="check_schedule" label="检查周期"></el-table-column>
            <el-table-column align="center" label="操作" fixed="right" width="200">
              <template #default="scope">
                <div class="flex justify-center flex-wrap">
                  <!-- <el-button size="small" color="#00706B" @click="handleEdit(scope.$index, scope.row)" v-auth="['websitemanage.validity.edit']">编辑</el-button> -->
                  <el-button size="small" color="#00706B" @click="handleSrc(scope.$index, scope.row)" v-auth="['websitemanage.validity.link']">外部链接</el-button>
                  <!-- <el-button v-auth="'SYSTEM_ADMIN'" size="small" type="primary" @click="handleExecute(scope.$index, scope.row)">执行</el-button> -->
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- Dialog 对话框 -->
          <el-dialog v-model="dialogVisible" width="650" center align-center destroy-on-close>
          <el-form class="w-xl" :model="form" ref="formRef" :rules="rules" label-position="right">
            <el-form-item label="归属单位" :label-width="formLabelWidth" prop="belonging_unit">
              <!-- <el-input readonly disabled v-model="form.belonging_unit" placeholder="请输入归属单位"></el-input> -->
              <div class="w-md">{{ form.belonging_unit }}</div>
            </el-form-item>
            <el-form-item label="网站名称" :label-width="formLabelWidth" prop="network_name">
              <!-- <el-input readonly disabled v-model="form.network_name" placeholder="请输入网站名称"></el-input> -->
              <div class="w-md">{{ form.network_name }}</div>
            </el-form-item>
            <el-form-item label="域名/IP" :label-width="formLabelWidth" prop="network_ip">
              <!-- <el-input readonly disabled v-model="form.network_ip" placeholder="请输入域名/IP"></el-input> -->
              <div class="w-md">{{ form.network_ip }}</div>
            </el-form-item>
            <el-form-item label="网络类型" :label-width="formLabelWidth" prop="network_type">
              <!-- <el-input readonly disabled v-model="form.network_type" placeholder="请选择网络类型"></el-input> -->
              <div class="w-md">{{ form.network_type }}</div>
            </el-form-item>
            <el-form-item required label="检查层级" :label-width="formLabelWidth" prop="check_level">
              <el-select v-model="form.check_level" placeholder="请选择检查层级">
                <el-option label="一级" value="一级"></el-option>
                <el-option label="二级" value="二级"></el-option>
                <el-option label="三级" value="三级"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="检查周期" :label-width="formLabelWidth" prop="check_schedule">
              <div class="w-md flex justify-between items-center">
                <!-- <div class="mr-6">每</div>
                <el-select :disabled="true" class="mr-3" v-model="form.time" placeholder="请选择检查周期">
                  <el-option label="年" value="年"></el-option>
                  <el-option label="月" value="月"></el-option>
                  <el-option label="周" value="周"></el-option>
                  <el-option label="天" value="天"></el-option>
                </el-select>
                <el-input :disabled="true" v-model="form.frequency" type="number"></el-input>
                <div class="ml-6">次</div> -->
                <div>每&nbsp;&nbsp;自然日&nbsp;&nbsp;1&nbsp;&nbsp;次</div>
              </div>
            </el-form-item>
          </el-form>
          <template #header="{ titleId }">
            <div :id="titleId" class="flex font-600 mb-6">编辑有效性规则</div>
          </template>
          <template #footer>
            <span class="dialog-footer">
              <el-button color="#00706B" @click="handleSubmit">保存</el-button>
              <el-button @click="dialogVisible = false">取消</el-button>
            </span>
          </template>
        </el-dialog>
        </div>

        <div class="flex justify-end">
          <el-pagination
            background
            layout="sizes, total, prev, pager, next"
            :page-sizes="[20, 30, 40, 50, 100]"
            :total="total"
            :page-count="pageCount"
            :page-size="pageSize"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          ></el-pagination>
        </div>
      </PageMain>
    </div>
  </template>

<route lang="yaml">
  meta:
    title: 站外网站名单
</route>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import websiteManageApi from '@/api/modules/websiteManagement'
import offsiteWebsiteManagement from '@/api/modules/offsiteWebsiteManagement'
import useBusinessStore from '@/store/modules/business'
import router from "@/router"
import importOrExportApi from '@/api/modules/importOrExport'
import { extractFilenameFromResponse, saveFileWithPicker } from '@/utils'
const { queryWebsiteData } = websiteManageApi
const { queryOffsiteWebsites, deleteOffsiteWebsites, addOffsiteWebsites, updateOffsiteWebsite, downloadOffsiteWebsiteExcel, uploadOffsiteWebsiteExcel } = offsiteWebsiteManagement
const businessStore = useBusinessStore()
const route = useRoute()
const { downloadOffsiteTemplate } = importOrExportApi

// 主表数据
let tableData = ref([])

// 工具项
const unit_name = ref('')
const network_name = ref('')
const unit_name_list = ref<any[]>([])

// 网页数据总条目数
const total = ref(100)
// 当前页码
const currentPage = ref(1)
// 总页码数量
const pageCount = ref(0)
// 每页显示的数据条目数
const pageSize = ref(20)

// 站外网站相关数据
const offsiteDialogVisible = ref(false)
const isEdit = ref(false)
const offsiteForm = reactive({
  id: '',
  name: '',
  url: ''
})

// 每个展开行的数据和分页状态
const expandedData = reactive<{
  [key: string]: {
    data: any[],
    total: number,
    currentPage: number,
    pageSize: number
  }
}>({})

const formLabelWidth = '120px'
const formRef = ref()

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入网站名称', trigger: 'blur' },
  ],
  url: [
    { required: true, message: '请输入网站域名', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback(new Error('请输入网站域名'))
        } else if (!validateDomainOrIP(value)) {
          callback(new Error('请输入有效的域名/URL格式'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

// 添加当前操作的网站ID
const currentNetworkId = ref('')

const id = route.query.websiteId as string

// 获取网站数据
const getWebsiteData = async (page_index: number, page_size: number, unit_name?: string, network_name?: string) => {
  const res = await queryWebsiteData({ page_index, page_size, unit_name, network_name })
  tableData.value = res?.data?.network_info_list
  total.value = res?.data?.total_num
  pageCount.value = res?.data?.page_num
}

// 获取站外网站数据
const getOffsiteWebsiteData = async (page: number, size: number) => {
  try {
    const res: any = await queryOffsiteWebsites(id, {
      page_index: page,
      page_size: size
    })

    if (res.code === 200 && res.res_code === '000000') {
      tableData.value = res?.data?.websites
      total.value = res?.data?.total_num
      pageCount.value = res?.data?.page_num
    } else {
      ElMessage.error(res.message || '获取站外网站数据失败')
    }
  } catch (error) {
    console.error('获取站外网站数据失败', error)
    ElMessage.error('获取站外网站数据失败')
  }
}

onMounted(async () => {
  // await getWebsiteData(currentPage.value, pageSize.value)
  await getOffsiteWebsiteData(currentPage.value, pageSize.value)
  await businessStore.getUnitNameList()
  unit_name_list.value = businessStore.unitNameOriginalList
})

// 主表事件处理
const handleQuery = async () => {
  await getWebsiteData(1, pageSize.value, unit_name.value, network_name.value)
}

const handlePageChange = async (val: number) => {
  currentPage.value = val
  await getOffsiteWebsiteData(currentPage.value, pageSize.value)
  // await getWebsiteData(currentPage.value, pageSize.value, unit_name.value, network_name.value)
}

const handleSizeChange = async (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  await getOffsiteWebsiteData(currentPage.value, pageSize.value)
  // await getWebsiteData(currentPage.value, pageSize.value, unit_name.value, network_name.value)
}

// 展开行相关事件处理
const handleExpandChange = async (row: any, expanded: boolean) => {
  if (expanded) {
    await getOffsiteWebsiteData(1, 20)
  }
}

const handleOffsitePageChange = async (network_info_id: string, page: number) => {
  const pageSize = expandedData[network_info_id]?.pageSize || 20
  await getOffsiteWebsiteData(page, pageSize)
}

const handleOffsiteSizeChange = async (network_info_id: string, size: number) => {
  await getOffsiteWebsiteData(1, size)
}

// 站外网站操作
const handleAdd = (network_info_id: string) => {
  isEdit.value = false
  offsiteDialogVisible.value = true
  currentNetworkId.value = network_info_id // 保存当前网站ID
  offsiteForm.id = ''
  offsiteForm.name = ''
  offsiteForm.url = ''
}

const handleEdit = (network_info_id: string, row: any) => {
  isEdit.value = true
  offsiteDialogVisible.value = true
  currentNetworkId.value = network_info_id // 保存当前网站ID
  offsiteForm.id = row.id
  offsiteForm.name = row.name
  offsiteForm.url = row.url
}

const handleDelete = (network_info_id: string, row: any) => {
  ElMessageBox.confirm(
    '是否确认删除该站外网站？',
    '警告',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      const res: any = await deleteOffsiteWebsites(network_info_id, row.id)
      if (res.code === 200 && res.res_code === '000000') {
        ElMessage.success('删除成功')
        await getOffsiteWebsiteData(
          expandedData[network_info_id]?.currentPage || 1,
          expandedData[network_info_id]?.pageSize || 20
        )
      } else {
        ElMessage.warning(res.message || res.data || '删除失败')
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

const handleSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (isEdit.value) {
        const res: any = await updateOffsiteWebsite(currentNetworkId.value, {
          id: offsiteForm.id,
          name: offsiteForm.name,
          url: offsiteForm.url
        })

        if (res.code === 200 && res.res_code === '000000') {
          ElMessage.success('编辑成功')
          offsiteDialogVisible.value = false
          await getOffsiteWebsiteData(
            expandedData[currentNetworkId.value]?.currentPage || 1,
            expandedData[currentNetworkId.value]?.pageSize || 20
          )
        } else {
          ElMessage.error(res.message || '编辑失败')
        }
      } else {
        const res: any = await addOffsiteWebsites(currentNetworkId.value, {
          websites: [{
            name: offsiteForm.name,
            url: offsiteForm.url
          }]
        })

        if (res.code === 200 && res.res_code === '000000') {
          ElMessage.success('新增成功')
          offsiteDialogVisible.value = false
          await getOffsiteWebsiteData(
            expandedData[currentNetworkId.value]?.currentPage || 1,
            expandedData[currentNetworkId.value]?.pageSize || 20
          )
        } else {
          ElMessage.warning(res.message || res.data || '新增失败')
        }
      }

      offsiteDialogVisible.value = false
      currentNetworkId.value = '' // 清空当前网站ID
      offsiteForm.id = ''
      offsiteForm.name = ''
      offsiteForm.url = ''
    } else {
      ElMessage.error('表单验证失败')
    }
  })
}

const handleCancel = () => {
  offsiteDialogVisible.value = false
  currentNetworkId.value = '' // 清空当前网站ID
  offsiteForm.id = ''
  offsiteForm.name = ''
  offsiteForm.url = ''
}

// Excel操作
const customUpload = async (network_info_id: string, params: any) => {
  const { file } = params
  const formData = new FormData()
  formData.append('file', file)

  try {
    const res: any = await uploadOffsiteWebsiteExcel(network_info_id, formData)
    if (res.code === 200) {
      if (res.res_code !== '000000') {
        ElMessage.warning(res.message)
        return
      }
      ElMessage.success('上传成功')
      await getOffsiteWebsiteData(
        expandedData[network_info_id]?.currentPage || 1,
        expandedData[network_info_id]?.pageSize || 20
      )
      return res
    } else {
      ElMessage.error(res.message || '上传失败')
      throw new Error(res.message || '上传失败')
    }
  } catch (error) {
    console.error('上传失败', error)
    ElMessage.error('上传失败')
    throw error
  }
}

const handleDownloadExcel = async (network_info_id: string) => {
  try {
    const res: any = await downloadOffsiteWebsiteExcel(network_info_id)
    const filename = extractFilenameFromResponse(res, '站外网站名单.xlsx')

    // 调用优化后的saveFileWithPicker
    const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.ms-excel')

    if (saveResult === true) {
      // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
    } else if (saveResult === 'default') {
      // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
    } else {
      // 用户取消操作
      ElMessage.info('下载已取消')
    }
  } catch (error) {
    console.error('下载失败', error)
    ElMessage.error('下载失败')
  }
}

const handleCancelEdit = () => {
  router.back()
}

// 导入弹窗相关的状态变量
const importDialogVisible = ref(false)
const uploadFile = ref(null)
const fileList = ref([])

// 下载外部链接编辑模板
const downloadTemplate = async () => {
  try {
    const res: any = await downloadOffsiteTemplate()
    const filename = extractFilenameFromResponse(res, '外部链接编辑模板.xlsx')

    // 调用优化后的saveFileWithPicker
    const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.ms-excel')

    if (saveResult === true) {
      // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
    } else if (saveResult === 'default') {
      // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
    } else {
      // 用户取消操作
      ElMessage.info('模板下载已取消')
    }
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('模板下载失败，请稍后重试')
  }
}

// 打开导入弹窗
const openImportDialog = () => {
  importDialogVisible.value = true
  fileList.value = []
  uploadFile.value = null
}

// 文件上传前的验证
const beforeUpload = (file: any) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件(格式为xlsx或xls)')
    return false
  }
  return true
}

// 文件变化处理
const handleFileChange = (file: any, uploadFileList: any) => {
  if (file.status === 'ready') {
    uploadFile.value = file.raw
    fileList.value = uploadFileList
  }
}

// 文件移除处理
const handleRemove = () => {
  uploadFile.value = null
  fileList.value = []
}

// 确认导入
const confirmImport = async () => {
  if (!uploadFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  try {
    const formData = new FormData()
    formData.append('file', uploadFile.value)

    await customUpload(id, { file: uploadFile.value })

    importDialogVisible.value = false
    uploadFile.value = null
    fileList.value = []
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请稍后重试')
  }
}

// 取消导入
const cancelImport = () => {
  importDialogVisible.value = false
  uploadFile.value = null
  fileList.value = []
}

// 域名/URL格式校验函数 - 支持域名后跟任意路径
// 域名/IP地址格式校验函数
const validateDomainOrIP = (value: string) => {
  if (!value) return false

  // IP格式：1-3位数字.1-3位数字.1-3位数字.1-3位数字
  const ipRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

  // IP+路径格式
  const ipPathRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

  // IP+端口号格式：IP地址:1-5位数字(端口号)
  const ipPortRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}$/

  // IP+端口号+路径格式
  const ipPortPathRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

  // 域名格式（支持中英文域名）
  const domainRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])$/

  // 域名+路径格式
  const domainPathRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

  // 域名+端口号格式
  const domainPortRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}$/

  // 域名+端口号+路径格式
  const domainPortPathRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

  // 带协议的URL格式（http://或https://开头）
  const urlRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])$/

  // 带协议的URL+路径格式
  const urlPathRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

  // 带协议的URL+端口号格式
  const urlPortRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}$/

  // 带协议的URL+端口号+路径格式
  const urlPortPathRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

  // 带协议的IP地址URL格式（http://或https://开头）
  const ipUrlRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

  // 带协议的IP地址URL+路径格式
  const ipUrlPathRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

  // 带协议的IP地址URL+端口号格式
  const ipUrlPortRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}$/

  // 带协议的IP地址URL+端口号+路径格式
  const ipUrlPortPathRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

  return ipRegex.test(value) ||
    ipPathRegex.test(value) ||
    ipPortRegex.test(value) ||
    ipPortPathRegex.test(value) ||
    domainRegex.test(value) ||
    domainPathRegex.test(value) ||
    domainPortRegex.test(value) ||
    domainPortPathRegex.test(value) ||
    urlRegex.test(value) ||
    urlPathRegex.test(value) ||
    urlPortRegex.test(value) ||
    urlPortPathRegex.test(value) ||
    ipUrlRegex.test(value) ||
    ipUrlPathRegex.test(value) ||
    ipUrlPortRegex.test(value) ||
    ipUrlPortPathRegex.test(value)
}
</script>

<template>
  <PageMain>
    <div class="flex justify-between">
      <div class="font-600">
        {{ route.query.networkName }}网站外部链接列表如下:
      </div>
      <div class="flex items-center">
        <el-button color="#00706B"
          @click="openImportDialog" v-auth="['websitemanage.validity.link.import']">导入</el-button>
        <el-button color="#00706B"
          @click="handleDownloadExcel(id)" v-auth="['websitemanage.validity.link.export']">导出</el-button>
        <el-button color="#00706B"
          @click="handleAdd(id)" v-auth="['websitemanage.validity.link.add']">新增</el-button>
        <el-button color="#00706B"
          @click="handleCancelEdit">返回</el-button>
      </div>
    </div>

    <!-- 主表 -->
    <div class="my-4">
      <!-- 站外网站表格 -->
      <el-table :data="tableData || []" border size="small"
        class="table-height-query">
        <el-table-column align="center" type="index" label="序号"
          width="50"></el-table-column>
        <!-- <el-table-column align="center" prop="id" label="ID" width="200"
                         show-overflow-tooltip></el-table-column> -->
        <el-table-column align="center" prop="name" label="网站名称"
          show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="url" label="网站域名"
          show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="操作" width="150">
          <template #default="scope">
            <div class="flex justify-center flex-wrap">
              <el-button size="small" color="#00706B"
                @click="handleEdit(id, scope.row)" v-auth="['websitemanage.validity.link.edit']">编辑</el-button>
              <el-button size="small" type="danger"
                @click="handleDelete(id, scope.row)" v-auth="['websitemanage.validity.link.delete']">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 站外网站分页 -->
    <div class="flex justify-end">
      <el-pagination background
        layout="sizes, total, prev, pager, next"
        :page-sizes="[20, 30, 40, 50, 100]" :page-size="pageSize"
        :total="total" :page-count="pageCount"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"></el-pagination>
    </div>

    <!-- 站外网站对话框 -->
    <el-dialog v-model="offsiteDialogVisible" width="650" center
      align-center destroy-on-close>
      <el-form class="w-xl" :model="offsiteForm" :rules="rules"
        ref="formRef" label-position="right">
        <el-form-item label="网站名称" :label-width="formLabelWidth"
          prop="name" required>
          <el-input v-model="offsiteForm.name"
            placeholder="请输入网站名称"></el-input>
        </el-form-item>
        <el-form-item label="网站域名" :label-width="formLabelWidth"
          prop="url">
          <el-input v-model="offsiteForm.url"
            placeholder="请输入网站域名"></el-input>
        </el-form-item>
      </el-form>
      <template #header="{ titleId }">
        <div :id="titleId" class="flex font-600 mb-6">
          {{ isEdit ? '编辑站外网站' : '新增站外网站' }}</div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B"
            @click="handleSubmit">保存</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入弹窗 -->
    <el-dialog v-model="importDialogVisible" width="500" center
      align-center destroy-on-close>
      <div class="mb-4 flex flex-col items-center">
        <div class="flex items-center mb-4">
          <span class="mr-2">导入模板:</span>
          <el-button type="text"
            @click="downloadTemplate">点击下载外部链接编辑模板</el-button>
        </div>

        <div class="flex items-center">
          <span class="mr-2">导入文件:</span>
          <div class="max-w-[360px]">
            <!-- 添加提示条件性显示 -->
            <div v-if="fileList.length > 0"
              class="mb-2 text-orange-500 text-xs">
              如需更换文件，请先点击文件右侧的"×"删除当前文件
            </div>
            <el-upload class="upload-demo" action="#"
              :auto-upload="false"
              :limit="1" :file-list="fileList"
              :on-change="handleFileChange" :on-remove="handleRemove"
              :before-upload="beforeUpload" accept=".xlsx,.xls">
              <!-- 只有当fileList为空时才允许上传 -->
              <el-button color="#00706B"
                :disabled="fileList.length > 0">文件上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传xlsx/xls文件
                </div>
              </template>
            </el-upload>
          </div>
        </div>
      </div>
      <template #header="{ titleId }">
        <div :id="titleId" class="flex font-600 mb-6">导入</div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B"
            @click="confirmImport">确认</el-button>
          <el-button @click="cancelImport">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </PageMain>
</template>
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'

export default function useAuth() {
  const settingsStore = useSettingsStore()
  const userStore = useUserStore()

  function hasPermission(permission: string) {
    // 通过访问permissionVersion来确保响应式更新
    userStore.permissionVersion // 触发响应式依赖

    if (settingsStore.settings.app.enablePermission) {
      console.log('userStore.permissions', userStore.permissions)
      return userStore.permissions.includes(permission)
    }
    else {
      return true
    }
  }

  function auth(value: string | string[]) {
    let auth
    if (typeof value === 'string') {
      auth = value !== '' ? hasPermission(value) : true
    }
    else {
      auth = value.length > 0 ? value.some(item => hasPermission(item)) : true
    }
    return auth
  }

  function authAll(value: string[]) {
    return value.length > 0 ? value.every(item => hasPermission(item)) : true
  }

  return {
    auth,
    authAll,
  }
}

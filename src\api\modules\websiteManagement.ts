import api from '../index'

export default {

  // 新增网站
  addWebsiteData: (data: {
      belonging_unit_id: string | number,
      network_name: string,
      network_ip: string,
      network_type: string,
    }) => api.post('/network_info', data),

    // 查询网站
    queryWebsiteData: (params: { network_name?: string, unit_name?: string, page_index: number, page_size: number }) => api.get('/network_info', {
      params: {
        ...params,
      },
    }),

    // 查询网站归属单位
    queryWebsiteUnit: () => api.get('/unit'),

    // 查询网站管理、临时任务管理、历史报告查询页面的归属单位数据
    queryNetworkUnit: () => api.get('/networkUnit'),

    //获取网站数据列表/查询网站数据列表
    websiteColumnList: (params: { network_name?: string, unit_name?: string, page_index: number, page_size: number }) => api.get('/network_info', {
      params: {
        ...params
      },
    }),

  //删除网站数据
  deleteWebsiteData: (data: { ids: string[] } | string) => api.delete('/network_info', { data }),

  // 清空网站数据
  clearWebsiteData: (id: string) => api.delete(`/network_info/clear/${id}`),

  //新增栏目/修改及时性规则
  addOrUpdateColumnData: (data: any) => {
    const url = '/network_column_info'
    return api.post(url, data);
  },

  //删除栏目
  deleteColumnData: (data: { ids: string[] }) => {
    const url = `/network_column_info`
    return api.delete(url, { data });
  },

  //查询栏目
  queryColumnData: (params: { network_id: string, page_index?: number, page_size?: number }) => {
    const url = `/network_column_info?network_id=${params.network_id}`;
    return api.get(url, {
      params: {
        page_index: params.page_index || '',
        page_size: params.page_size || '',
      },
    });
  },

  // 执行检查
  executeCheck: (data: {
    website_id: string,
    column_ids: string[],
  }) => api.post('/news_update', data),

  // 网站栏目导入
  importWebsiteColumn: (network_id: string, formData: FormData) => {
    return api.post(`/doexcel?network_id=${network_id}`, formData);
  },

  // 网站栏目导出
  exportWebsiteColumn: (network_id: string) => {
    return api.get(`/doexcel?network_id=${network_id}`, {
      headers: {
        'Content_type': 'application/octet-stream'
      },
      responseType: 'blob'
    })
  },

  // 查看网站及对应有效性规则
  queryWebsiteEffectRule: (params: { page_index: number, page_size: number, id?: string }) => api.get(`/effec_check_rule/${params.id}`, {
    params: {
      page_index: params.page_index,
      page_size: params.page_size,
    },
  }),

  // 修改网站有效性规则
  updateWebsiteEffectRule: (data: {
    check_level: string,
    check_schedule?: string,
    network_id: string
  }) => api.post(`/effec_check_rule/${data.network_id}`, {
      check_level:  data.check_level,
      check_schedule: data.check_schedule || ''
  }),

  // 执行有效性规则
  executeWebsiteEffectRule: (network_id: string) => api.get(`/effec_check_run/${network_id}`),

  // 执行有效性规则(异步执行)
  asyncExecuteWebsiteEffectRule: (network_id: string) => api.post(`/effec_check_rule_schedule/${network_id}`),

  // 修改报告规则
  updateWebsiteReportRule: (data: {
    check_schedule: string,
    check_result?: string,
    network_id: string
  }) => api.post(`/report/${data.network_id}`, {
    check_result:  data.check_result,
    check_schedule: data.check_schedule || ''
  }),

  // 查询网站及对应的报告规则
  queryWebsiteReportRule: (params: { page_index: number, page_size: number, unit_name?: string, report_time?: string }) => api.get('/report', {
    params: {
      ...params,
    },
  }),

  // 生成报告
  generateWebsiteReport: (network_id: string, check_schedule: string) => api.get(`/generate/report/${network_id}?check_schedule=${check_schedule}`, {
    responseType: 'blob',
  }),

  // 下载报告
  downloadWebsiteReport: (network_id: string, check_schedule: string) => api.get(`/download/report/${network_id}?check_schedule=${check_schedule}`, {
    responseType: 'blob',
  }),
}

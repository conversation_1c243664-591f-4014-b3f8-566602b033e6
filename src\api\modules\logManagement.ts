import api from '../index'

export default {
  // 查看日志文件列表
  queryLogFiles: (params: {
    page_index?: number,
    page_size?: number
  }) => api.get('/logs', { params: { ...params } }),

  // 下载特定日志文件
  downloadLogFile: (fileName: string) => api.get(`/logs?file_name=${fileName}`, {
    headers: {
      'Content-Type': 'application/octet-stream'
    },
    responseType: 'blob'
  }),

  // 删除指定名称的日志
  deleteLogFiles: (fileNames: string[]) => api.delete(`/logs?file_names=${fileNames.join(',')}`),
}

<route lang="yaml">
meta:
  title: 网站管理
</route>

<script setup lang="ts">
  import { ElMessage, ElMessageBox } from 'element-plus'
  import websiteManageApi from '@/api/modules/websiteManagement'
  import importOrExportApi from '@/api/modules/importOrExport'
  import useBusinessStore from '@/store/modules/business'
  import { extractFilenameFromResponse, saveFileWithPicker } from '@/utils'
  const router = useRouter()
  const { queryWebsiteData, queryNetworkUnit, deleteWebsiteData, addWebsiteData, clearWebsiteData } = websiteManageApi
  const { downloadWebsiteTemplate, importWebsiteExcel, exportWebsiteExcel } = importOrExportApi
  const businessStore = useBusinessStore()
  // 数据
  let tableData = ref([])
  // 选中的行数据
  const selectedRows = ref<any[]>([])

  // 工具项
  const unit_name = ref('');
  const network_name = ref('');

  const unit_name_list = ref<any[]>([])

  // 网页数据总条目数
  const total = ref(100);
  // 当前页码
  const currentPage = ref(1);
  // 总页码数量
  const pageCount = ref(0)
  // 每页显示的数据条目数
  const pageSize = ref(20)

  // Dialog 对话框
const dialogVisible = ref(false);
  // 导入对话框
  const importDialogVisible = ref(false);

const form = reactive({
  belonging_unit_id: '',
  network_name: '',
  network_ip: '',
  network_type: ''
});

  // 导入文件相关
  const uploadFile = ref(null);
  const fileList = ref([]);

const formLabelWidth = '120px';
const formRef = ref();

// 表单验证规则
const rules = reactive({
  belonging_unit_id: [{ required: true, message: '请输入归属单位', trigger: 'blur' }],
  network_name: [{ required: true, message: '请输入网站名称', trigger: 'blur' }],
  network_ip: [
    { required: true, message: '请输入域名/IP', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback(new Error('请输入域名/IP'))
        } else if (!validateDomainOrIP(value)) {
          callback(new Error('请输入有效的域名/IP地址格式'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  network_type: [{ required: true, message: '请选择网络类型', trigger: 'change' }]
});

onMounted(async () => {
  await getWebsiteData(currentPage.value, pageSize.value)
  await businessStore.getNetworkUnitList()
  unit_name_list.value = businessStore.networkUnitOriginalList
  // 添加获取所属单位列表的调用
  await businessStore.getUnitNameList()
})

const getWebsiteData = async (page_index: number, page_size: number, unit_name?: string, network_name?: string) => {
  const res = await queryWebsiteData({ page_index, page_size, unit_name, network_name })
  tableData.value = res?.data?.network_info_list
  total.value = res?.data?.total_num
  pageCount.value = res?.data?.page_num
}

  // 事件处理
  const handleQuery = async () => {
    const res = await queryWebsiteData({ page_index: 1, page_size: 20, unit_name: unit_name.value, network_name: network_name.value })
    tableData.value = res?.data?.network_info_list
    // total.value = res?.data?.total_num
    // pageCount.value = res?.data?.page_num
  };

  // 表格选择变化处理
  const handleSelectionChange = (selection: any[]) => {
    selectedRows.value = selection
  }

  // 导入相关方法
  const handleImport = () => {
    importDialogVisible.value = true;
    fileList.value = [];
  };

  // 下载网站信息编辑模板
  const handleDownloadTemplate = async () => {
    try {
      const res = await downloadWebsiteTemplate();
      // 从Content-Disposition头中提取文件名并进行URL解码
      const filename = extractFilenameFromResponse(res, '网站信息模板.xlsx');
      
      // 调用优化后的saveFileWithPicker
      const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      
      if (saveResult === true) {
        // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
      } else if (saveResult === 'default') {
        // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
      } else {
        // 用户取消操作
        ElMessage.info('模板下载已取消')
      }
    } catch(error) {
      console.error('下载模板失败:', error);
      ElMessage.error('模板下载失败，请稍后重试');
    }
  };

  // 文件上传前的验证
  const beforeUpload = (file: any) => {
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                    file.type === 'application/vnd.ms-excel';
    if (!isExcel) {
      ElMessage.error('只能上传Excel文件(格式为xlsx或xls)');
      return false;
    }
    return true;
  };

  // 文件变化处理
  const handleFileChange = (file: any, uploadFileList: any) => {
  if (file.status === 'ready') {
    uploadFile.value = file.raw
    fileList.value = uploadFileList
  }
}

  // 文件移除处理
  const handleRemove = () => {
    uploadFile.value = null
    fileList.value = []
  }

  // 确认导入
  const confirmImport = async () => {
    if (!uploadFile.value) {
      ElMessage.warning('请选择要导入的文件');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('file', uploadFile.value);

      const res: any = await importWebsiteExcel(formData);
      if (res?.code === 200 && res?.res_code === '000000') {
        ElMessage.success('网站信息导入成功');
        importDialogVisible.value = false;
        // 刷新数据
        await getWebsiteData(currentPage.value, pageSize.value);
      } else {
        ElMessage.warning(res?.message || '导入失败，请检查文件格式');
      }
    } catch (error) {
      console.error('导入失败:', error);
      ElMessage.error('导入失败，请稍后重试');
    }
  };

  // 取消导入
  const cancelImport = () => {
    importDialogVisible.value = false;
    uploadFile.value = null;
    fileList.value = [];
  };

  // 导出Excel
  const handleExport = async () => {
    try {
      const res = await exportWebsiteExcel();
      console.log('导出excel', res)
      // 使用从@index.ts导入的工具函数提取文件名
      const filename = extractFilenameFromResponse(res, '网站信息.xlsx');
      
      // 调用优化后的saveFileWithPicker
      const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      
      if (saveResult === true) {
        // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
      } else if (saveResult === 'default') {
        // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
      } else {
        // 用户取消操作
        ElMessage.info('导出已取消')
      }
    } catch (error) {
      console.error('导出失败:', error);
      ElMessage.error('导出失败，请稍后重试');
    }
  };

  // 下载文件工具方法
  const downloadFile = (data: any, fileName: string) => {
    const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    link.click();
    URL.revokeObjectURL(link.href);
  };

  const handleAdd = () => {
    dialogVisible.value = true;
  };

  const handleDetails = (index: number, row: any) => {
    router.push({
      name: 'websiteEdit',
      query: {
        websiteId: row.id,
        networkName: row.network_name,
        networkType: row.network_type
      }
    })
  };

  const handleEdit = (index: number, row: any) => {
    router.push({
      name: 'validtyRuleConfig',
      query: {
        websiteId: row.id,
        networkName: row.network_name,
        networkType: row.network_type
      }
    })
  };



  // 批量删除方法
  const handleBatchDelete = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要删除的网站信息')
      return
    }

    ElMessageBox.alert(
      `是否确认删除以上网站信息?网站及时性与有效性将被同步删除？`,
      '提示',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        // 提取选中行的ID
        const ids = selectedRows.value.map((row: any) => row.id)
        const res: any = await deleteWebsiteData({ ids })
        if (res.code === 200 && res.res_code === '000000') {
          ElMessage.success('删除成功');
          selectedRows.value = []
          currentPage.value = 1
          pageSize.value = 20
          await getWebsiteData(currentPage.value, pageSize.value)
          await businessStore.getNetworkUnitList()
          unit_name_list.value = businessStore.networkUnitOriginalList
          // 更新所属单位列表
          await businessStore.getUnitNameList()
        } else {
          ElMessage.warning(res.message || '删除失败');
        }
      })
      .catch(() => {
        // 用户点击取消，不执行任何操作
        ElMessage.info('已取消删除')
      })
  };

  // 添加清空方法
  const handleClear = (index: number, row: any) => {
    // 添加确认弹窗
    ElMessageBox.alert(
      '是否确认清空当前网站数据？',
      '警告',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
      .then(async () => {
        // 用户点击确认，执行清空操作
        const res: any = await clearWebsiteData(row.id)
        if (res.code === 200 && res.res_code === '000000') {
          ElMessage.success('清空成功');
          currentPage.value = 1
          pageSize.value = 20
          await getWebsiteData(currentPage.value, pageSize.value)
          await businessStore.getNetworkUnitList()
          unit_name_list.value = businessStore.networkUnitOriginalList
          // 更新所属单位列表
          await businessStore.getUnitNameList()
        } else {
          ElMessage.warning(res.message || '清空失败');
        }
      })
      .catch(() => {
        // 用户点击取消，不执行任何操作
        ElMessage.info('已取消清空')
      })
  };

  const handlePageChange = async (val: number) => {
    currentPage.value = val;
    await getWebsiteData(currentPage.value, pageSize.value)
  };

  const handleSizeChange = async (val: number) => {
    pageSize.value = val;
    await getWebsiteData(currentPage.value, pageSize.value)
  };

  const handleSubmit = () => {
  formRef.value.validate(async (valid: any) => {
    if (valid) {
        if (!form.belonging_unit_id) {
          ElMessage.warning('请选择有效的归属单位')
          return
        }

      const res: any = await addWebsiteData(form)
      if (res.code === 200 && res.res_code !== '000000') {
        ElMessage.warning(res.message)
        return
      } else if (res.code === 200 && res.res_code === '000000') {
        ElMessage.success('保存成功');
        currentPage.value = 1
        pageSize.value = 20
        await getWebsiteData(currentPage.value, pageSize.value)
          await businessStore.getNetworkUnitList()
          unit_name_list.value = businessStore.networkUnitOriginalList
          // 更新所属单位列表
        await businessStore.getUnitNameList()
      }
      dialogVisible.value = false;
        resetForm();
    } else {
      ElMessage.error('表单验证失败');
    }
  });
};

  // 重置表单方法
  const resetForm = () => {
    form.belonging_unit_id = '';
  form.network_name = '';
  form.network_type = '';
  form.network_ip = '';
}

  // 单位选择变化处理函数
  const handleUnitChange = (unitName: string) => {
    console.log(unitName);
    const selectedUnit = businessStore.unitNameOriginalList.find(item => item.unit_name === unitName)
    if (selectedUnit) {
      form.belonging_unit_id = selectedUnit.id
    } else {
      form.belonging_unit_id = ''
    }
  }

  const handleCancel = () => {
    dialogVisible.value = false;
    resetForm();
  }

  // 域名/IP地址格式校验函数
  const validateDomainOrIP = (value: string) => {
    if (!value) return false

    // IP格式：1-3位数字.1-3位数字.1-3位数字.1-3位数字
    const ipRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

    // IP+路径格式
    const ipPathRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // IP+端口号格式：IP地址:1-5位数字(端口号)
    const ipPortRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}$/

    // IP+端口号+路径格式
    const ipPortPathRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 域名格式（支持中英文域名）
    const domainRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])$/

    // 域名+路径格式
    const domainPathRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 域名+端口号格式
    const domainPortRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}$/

    // 域名+端口号+路径格式
    const domainPortPathRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 带协议的URL格式（http://或https://开头）
    const urlRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])$/

    // 带协议的URL+路径格式
    const urlPathRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 带协议的URL+端口号格式
    const urlPortRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}$/

    // 带协议的URL+端口号+路径格式
    const urlPortPathRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 带协议的IP地址URL格式（http://或https://开头）
    const ipUrlRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

    // 带协议的IP地址URL+路径格式
    const ipUrlPathRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 带协议的IP地址URL+端口号格式
    const ipUrlPortRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}$/

    // 带协议的IP地址URL+端口号+路径格式
    const ipUrlPortPathRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    return ipRegex.test(value) ||
            ipPathRegex.test(value) ||
            ipPortRegex.test(value) ||
            ipPortPathRegex.test(value) ||
            domainRegex.test(value) ||
            domainPathRegex.test(value) ||
            domainPortRegex.test(value) ||
            domainPortPathRegex.test(value) ||
            urlRegex.test(value) ||
            urlPathRegex.test(value) ||
            urlPortRegex.test(value) ||
            urlPortPathRegex.test(value) ||
            ipUrlRegex.test(value) ||
            ipUrlPathRegex.test(value) ||
            ipUrlPortRegex.test(value) ||
            ipUrlPortPathRegex.test(value)
  }
</script>

<template>
  <PageMain>
    <div class="flex justify-between" v-auth="['websitemanage.query']">
        <div class="w-auto flex">
          <div class="flex items-center mr-6">
            <div class="mr-6 font-600">归属单位</div>
            <el-tree-select
            style="width: 200px;"
              v-model="unit_name"
              placeholder="请选择归属单位"
              filterable
              :props="{value: 'label'}"
               :data="unit_name_list"
               :render-after-expand="false"
               clearable
              >
            </el-tree-select>
            <!-- <el-select clearable v-model="unit_name" placeholder="请选择归属单位" style="width: 200px;">
              <el-option v-for="(item, index) in unit_name_list" :key="index" :label="item" :value="item"></el-option>
            </el-select> -->
          </div>

          <!-- <div class="flex items-center mr-6">
            <div class="mr-6 font-600">网站名称</div>
            <el-input clearable v-model="network_name" placeholder="请输入网站名称" style="width: 200px;"></el-input>
          </div> -->

          <div class="flex items-center">
            <el-button color="#00706B" @click="handleQuery">查询</el-button>
          </div>
        </div>
    </div>

    <!-- 表格 -->
    <div class="my-4">
      <div class="flex items-center justify-between my-4">
        <h2 class="text-lg font-bold mb-4">网站列表</h2>
        <div class="flex items-center">
          <el-button color="#00706B" @click="handleImport" v-auth="['websitemanage.import']">导入</el-button>
          <el-button color="#00706B" @click="handleExport" v-auth="['websitemanage.export']">导出</el-button>
          <el-button color="#00706B" @click="handleAdd" v-auth="['websitemanage.add']">新增</el-button>
          <el-button color="#00706B" @click="handleBatchDelete" v-auth="['websitemanage.delete']" :disabled="!selectedRows?.length">删除</el-button>
        </div>
      </div>
      <el-table :data="tableData" border size="small" class="table-height" @selection-change="handleSelectionChange">
        <el-table-column type="selection" fixed="left" width="50"></el-table-column>
        <el-table-column align="center" type="index" label="序号" fixed="left" width="50"></el-table-column>
        <el-table-column align="center" prop="belonging_unit" label="归属单位"></el-table-column>
        <el-table-column align="center" prop="network_name" label="网站名称" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="network_ip" label="域名/IP" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="network_type" label="网络类型"></el-table-column>
        <el-table-column align="center" prop="first_column_count" label="一级栏目数量"></el-table-column>
        <el-table-column align="center" prop="second_column_count" label="二级栏目数量"></el-table-column>
        <el-table-column align="center" prop="third_column_count" label="三级栏目数量"></el-table-column>
        <el-table-column align="center" prop="updated_at" label="上次编辑时间" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="200">
          <template #default="scope">
            <div class="flex justify-center flex-wrap">
              <el-button size="small" color="#00706B" @click="handleDetails(scope.$index, scope.row)" v-auth="['websitemanage.timeliness']">及时性</el-button>
              <el-button size="small" color="#00706B" @click="handleEdit(scope.$index, scope.row)" v-auth="['websitemanage.validity']">有效性</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 翻页组件 -->
    <div class="flex justify-end">
      <el-pagination
        background
        layout="sizes, total, prev, pager, next"
        :page-sizes="[20, 30, 40, 50, 100]"
        :total="total"
        :page-count="pageCount"
        :page-size="pageSize"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      ></el-pagination>
    </div>

    <!-- 新增网站对话框 -->
      <el-dialog v-model="dialogVisible" width="650" center align-center destroy-on-close>
        <el-form class="w-xl" :model="form" :rules="rules" ref="formRef" label-position="right">
          <el-form-item label="归属单位" :label-width="formLabelWidth" prop="belonging_unit_id">
            <el-tree-select
              v-model="form.belonging_unit_id"
              placeholder="请选择归属单位"
              filterable
               :data="businessStore.unitNameOriginalList"
               :render-after-expand="false"
              >
            </el-tree-select>
          </el-form-item>
          <el-form-item label="网站名称" :label-width="formLabelWidth" prop="network_name">
            <el-input v-model="form.network_name" placeholder="请输入网站名称"></el-input>
          </el-form-item>
          <el-form-item label="域名/IP" :label-width="formLabelWidth" prop="network_ip">
            <el-input v-model="form.network_ip" placeholder="请输入域名/IP"></el-input>
          </el-form-item>
          <el-form-item label="网络类型" :label-width="formLabelWidth" prop="network_type">
            <el-select v-model="form.network_type" placeholder="请选择网络类型">
              <el-option label="内网" value="内网"></el-option>
              <el-option label="外网" value="外网"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #header="{ titleId }">
          <div :id="titleId" class="flex font-600 mb-6">新增网站</div>
        </template>
        <template #footer>
          <span class="dialog-footer">
            <el-button color="#00706B" @click="handleSubmit">保存</el-button>
            <el-button @click="handleCancel">取消</el-button>
          </span>
        </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog v-model="importDialogVisible" width="500" center align-center destroy-on-close>
      <div class="mb-4 flex flex-col items-center">
        <div class="flex items-center mb-4">
          <span class="mr-2">导入模板:</span>
          <el-button type="text" @click="handleDownloadTemplate">点击下载网站信息编辑模板</el-button>
        </div>

        <div class="flex items-center">
          <span class="mr-2">导入文件:</span>
          <div class="max-w-[360px]">
            <!-- 添加提示条件性显示 -->
            <div v-if="fileList.length > 0" class="mb-2 text-orange-500 text-xs">
              如需更换文件，请先点击文件右侧的"×"删除当前文件
            </div>
            <el-upload
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :limit="1"
              :file-list="fileList"
              :on-change="handleFileChange"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              accept=".xlsx,.xls"
            >
              <!-- 只有当fileList为空时才允许上传 -->
              <el-button color="#00706B" :disabled="fileList.length > 0">文件上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传xlsx/xls文件
                </div>
              </template>
            </el-upload>
          </div>
        </div>
      </div>
      <template #header="{ titleId }">
        <div :id="titleId" class="flex font-600 mb-6">导入</div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="confirmImport">确认</el-button>
          <el-button @click="cancelImport">取消</el-button>
          </span>
        </template>
    </el-dialog>
  </PageMain>
</template>

<script setup lang="ts">
import roleManageApi from '@/api/modules/roleManage'
import importOrExportApi from '@/api/modules/importOrExport'
import { ElMessage, ElMessageBox } from 'element-plus'
import { nextTick } from 'vue'
import { extractFilenameFromResponse, saveFileWithPicker } from '@/utils'

defineOptions({
  name: 'UserRoleManageVue',
})

// 表格数据
const tableData = ref([])
// 选中的行数据
const selectedRows = ref<any[]>([])

// 分页相关变量
const total = ref(0)
const currentPage = ref(1)
const pageCount = ref(0)
const pageSize = ref(20)

// 加载状态
const loading = ref(false)

// 新增角色弹窗控制
const addDialogVisible = ref(false)
// 编辑角色弹窗控制
const editDialogVisible = ref(false)
// 操作权限弹窗控制
const permissionDialogVisible = ref(false)
// 导入弹窗显示控制
const importDialogVisible = ref(false)

// 新增角色表单数据
const addForm = reactive({
  name: ''
})

// 编辑角色表单数据
const editForm = reactive({
  id: '',
  originalName: '',
  newName: ''
})

// 导入文件相关
const uploadFile = ref(null)
const fileList = ref([])

// 操作权限相关数据
const currentRoleForPermission = ref<any>(null)
const permissionTreeData = ref([])
const permissionLoading = ref(false)
// 新增权限保存状态，与加载状态分离
const permissionSaving = ref(false)
const defaultCheckedKeys = ref<string[]>([])
const permissionTreeRef = ref()

// 表单引用
const addFormRef = ref()
const editFormRef = ref()

// 新增角色表单验证规则
const addRules = reactive({
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ]
})

// 编辑角色表单验证规则
const editRules = reactive({
  newName: [
    { required: true, message: '请输入新角色名称', trigger: 'blur' }
  ]
})

// 获取角色列表
const getRoleList = async () => {
  try {
    loading.value = true
    const res: any = await roleManageApi.getRoleList()

    if (res?.code === 200 && res?.res_code === '000000') {
      const data = res.data
      tableData.value = data.page_list || []
      total.value = data.total_num || 0
      pageCount.value = data.page_num || 0
    } else {
      ElMessage.error(res?.message || '获取角色列表失败')
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理页码变化
const handlePageChange = async (val: number) => {
  currentPage.value = val
  await getRoleList()
}

// 处理每页数量变化
const handleSizeChange = async (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  await getRoleList()
}

// 打开导入弹窗
const handleImport = () => {
  importDialogVisible.value = true
  fileList.value = []
}

// 下载角色信息导入模板
const downloadTemplate = async () => {
  try {
    const res = await importOrExportApi.downloadRoleTemplate()
    const filename = extractFilenameFromResponse(res, '角色信息导入模板.xlsx')
    
    // 调用优化后的saveFileWithPicker
    const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    
    if (saveResult === true) {
      // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
    } else if (saveResult === 'default') {
      // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
    } else {
      // 用户取消操作
      ElMessage.info('模板下载已取消')
    }
  } catch(error) {
    console.error('下载模板失败:', error)
    ElMessage.error('模板下载失败，请稍后重试')
  }
}

// 下载文件工具方法
const downloadFile = (data: any, fileName: string) => {
  const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = fileName
  link.click()
  URL.revokeObjectURL(link.href)
}

// 文件上传前的验证
const beforeUpload = (file: any) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件(格式为xlsx或xls)')
    return false
  }
  return true
}

// 文件变化处理，确保uploadFile与fileList同步
const handleFileChange = (file: any, uploadFileList: any) => {
  if (file.status === 'ready') {
    uploadFile.value = file.raw
    fileList.value = uploadFileList
  }
}

// 文件移除处理
const handleRemove = () => {
  uploadFile.value = null
  fileList.value = []
}

// 确认导入前再次检查文件状态
const confirmImport = async () => {
  if (!uploadFile.value || fileList.value.length === 0) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  try {
    const formData = new FormData()
    formData.append('file', uploadFile.value)

    const res: any = await importOrExportApi.importRoleExcel(formData)
    if (res?.code === 200 && res.res_code === '000000') {
      ElMessage.success(res.message || '角色信息导入成功')
      importDialogVisible.value = false
      // 刷新数据
      await getRoleList()
    } else {
      ElMessage.warning(res?.message || res.data || '导入失败，请检查文件格式或查看文件是否有未填项')
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请稍后重试')
  }
}

// 取消导入
const cancelImport = () => {
  importDialogVisible.value = false
  uploadFile.value = null
  fileList.value = []
}

// 导出角色Excel
const handleExport = async () => {
  try {
    const res = await importOrExportApi.exportRoleExcel()
    const filename = extractFilenameFromResponse(res, '角色信息.xlsx')

    // 调用优化后的saveFileWithPicker
    const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    if (saveResult === true) {
      // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
    } else if (saveResult === 'default') {
      // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
    } else {
      // 用户取消操作
      ElMessage.info('导出已取消')
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

// 打开新增角色弹窗
const handleAdd = () => {
  addDialogVisible.value = true
  // 重置表单
  addForm.name = ''
}

// 打开编辑角色弹窗
const handleEdit = (row: any) => {
  editDialogVisible.value = true
  // 设置编辑数据
  editForm.id = row.id
  editForm.originalName = row.name
  editForm.newName = '' // 不回显，让用户输入新的角色名称
}

// 新增角色提交
const handleAddSubmit = () => {
  addFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const res: any = await roleManageApi.addRole({
          datalist: [{ name: addForm.name }]
        })

        if (res?.code === 200 && res?.res_code === '000000') {
          ElMessage.success(res?.message || '新增角色成功')
          addDialogVisible.value = false
          // 重置表单
          addForm.name = ''
          // 刷新列表
          await getRoleList()
        } else {
          ElMessage.error(res?.message || '新增角色失败')
        }
      } catch (error) {
        console.error('新增角色失败:', error)
        ElMessage.error('新增角色失败，请稍后重试')
      }
    }
  })
}

// 新增角色取消
const handleAddCancel = () => {
  addDialogVisible.value = false
  // 重置表单
  addForm.name = ''
}

// 编辑角色提交
const handleEditSubmit = () => {
  editFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const res: any = await roleManageApi.updateRole({
          id: editForm.id,
          name: editForm.newName
        })

        if (res?.code === 200 && res?.res_code === '000000') {
          ElMessage.success(res?.message || '编辑角色成功')
          editDialogVisible.value = false
          // 重置表单
          editForm.id = ''
          editForm.originalName = ''
          editForm.newName = ''
          // 刷新列表
          await getRoleList()
        } else {
          ElMessage.error(res?.message || '编辑角色失败')
        }
      } catch (error) {
        console.error('编辑角色失败:', error)
        ElMessage.error('编辑角色失败，请稍后重试')
      }
    }
  })
}

// 编辑角色取消
const handleEditCancel = () => {
  editDialogVisible.value = false
  // 重置表单
  editForm.id = ''
  editForm.originalName = ''
  editForm.newName = ''
}

// 打开操作权限弹窗
const handlePermission = async (row: any) => {
  currentRoleForPermission.value = row
  permissionDialogVisible.value = true

  // 获取角色权限数据
  await getRolePermissions(row.id)
}

// 递归收集所有选中的节点ID
const collectCheckedKeys = (nodes: any[]): string[] => {
  const checkedKeys: string[] = []

  const traverse = (nodeList: any[]) => {
    nodeList.forEach(node => {
      if (node.selected) {
        checkedKeys.push(node.id)
      }
      if (node.operations && node.operations.length > 0) {
        traverse(node.operations)
      }
    })
  }

  traverse(nodes)
  return checkedKeys
}

// 获取角色权限数据
const getRolePermissions = async (roleId: string) => {
  try {
    permissionLoading.value = true
    const res: any = await roleManageApi.getRolePermissions(roleId)

    console.log('角色权限数据:', res)

    if (res?.code === 200 && res?.res_code === '000000') {
      permissionTreeData.value = res.data || []

      // 收集默认选中的节点
      defaultCheckedKeys.value = collectCheckedKeys(res.data || [])
      console.log('处理后的权限树数据:', permissionTreeData.value)
      console.log('默认选中的节点ID:', defaultCheckedKeys.value)

      // 下次tick后设置选中状态，确保树形控件已渲染
      nextTick(() => {
        if (permissionTreeRef.value) {
          permissionTreeRef.value.setCheckedKeys(defaultCheckedKeys.value)
        }
      })
    } else {
      ElMessage.error(res?.message || '获取角色权限失败')
    }
  } catch (error) {
    console.error('获取角色权限失败:', error)
    ElMessage.error('获取角色权限失败，请稍后重试')
  } finally {
    permissionLoading.value = false
  }
}

// 权限弹窗保存
const handlePermissionSave = async () => {
  // 基础验证
  if (!permissionTreeRef.value || !currentRoleForPermission.value) {
    ElMessage.error('缺少必要的权限数据或角色信息')
    return
  }

  // 验证权限树数据是否加载完成
  if (!permissionTreeData.value || permissionTreeData.value.length === 0) {
    ElMessage.error('权限数据未加载完成，请稍后再试')
    return
  }

  // 防止重复点击
  if (permissionSaving.value) {
    return
  }

  try {
    permissionSaving.value = true

    // 获取选中的节点ID
    const checkedKeys = permissionTreeRef.value.getCheckedKeys() || []
    const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys() || []

    console.log('保存权限配置:', {
      roleId: currentRoleForPermission.value.id,
      roleName: currentRoleForPermission.value.name,
      checkedKeys: checkedKeys,
      halfCheckedKeys: halfCheckedKeys,
      totalSelected: checkedKeys.length,
      totalHalfSelected: halfCheckedKeys.length
    })

    // 验证角色ID的有效性
    if (!currentRoleForPermission.value.id) {
      ElMessage.error('角色ID无效，无法保存权限配置')
      return
    }

    // 调用接口保存权限配置
    const res: any = await roleManageApi.updateRolePermissions({
      role_id: currentRoleForPermission.value.id,
      operation: checkedKeys
    })

    if (res?.code === 200 && res?.res_code === '000000') {
      ElMessage.success(res?.message || '权限配置保存成功')
      permissionDialogVisible.value = false

      // 重置权限相关数据
      currentRoleForPermission.value = null
      permissionTreeData.value = []
      defaultCheckedKeys.value = []

      // 刷新角色列表，确保数据一致性
      try {
        await getRoleList()
      } catch (error) {
        console.warn('刷新角色列表失败:', error)
        // 不影响主要的保存成功流程，只是警告
      }
    } else {
      // 保存失败，保持弹窗打开状态，让用户可以重试
      const errorMessage = res?.message || '权限配置保存失败'
      console.error('权限保存失败:', res)
      ElMessage.error(errorMessage)
    }
  } catch (error) {
    console.error('权限配置保存失败:', error)

    // 根据错误类型提供更具体的提示
    let errorMessage = '权限配置保存失败，请稍后重试'
    if (error instanceof TypeError && error.message.includes('fetch')) {
      errorMessage = '网络连接失败，请检查网络后重试'
    } else if (error instanceof Error && error.message.includes('timeout')) {
      errorMessage = '请求超时，请稍后重试'
    }

    ElMessage.error(errorMessage)
  } finally {
    permissionSaving.value = false
  }
}

// 权限弹窗取消
const handlePermissionCancel = () => {
  // 如果正在保存中，提醒用户等待
  if (permissionSaving.value) {
    ElMessage.warning('权限配置保存中，请稍等片刻')
    return
  }

  // 重置所有权限相关状态
  permissionDialogVisible.value = false
  currentRoleForPermission.value = null
  permissionTreeData.value = []
  defaultCheckedKeys.value = []
  permissionLoading.value = false
  permissionSaving.value = false
}

// 权限弹窗关闭前的处理逻辑
const handlePermissionDialogClose = () => {
  // 如果正在保存中，阻止关闭并提醒用户等待
  if (permissionSaving.value) {
    ElMessage.warning('权限配置保存中，请稍等片刻')
    return
  }

  // 调用取消逻辑
  handlePermissionCancel()
}

// 添加一个标志来防止递归调用
const isUpdatingCheckState = ref(false)

// 获取节点层级（1=一级，2=二级，3=三级，4=四级）
const getNodeLevel = (nodeId: string, data: any[], level: number = 1): number => {
  for (const item of data) {
    if (item.id === nodeId) {
      return level
    }
    if (item.operations && item.operations.length > 0) {
      const foundLevel = getNodeLevel(nodeId, item.operations, level + 1)
      if (foundLevel > 0) {
        return foundLevel
      }
    }
  }
  return 0
}

// 获取节点的所有父节点（从直接父节点到根节点）
const getAllParentNodes = (nodeId: string, data: any[], currentPath: any[] = []): any[] => {
  for (const item of data) {
    // 检查当前节点的直接子节点
    if (item.operations && item.operations.length > 0) {
      for (const child of item.operations) {
        if (child.id === nodeId) {
          // 找到目标节点，返回从根到父节点的路径
          return [...currentPath, item]
        }
      }

      // 递归查找更深层的子节点
      const result = getAllParentNodes(nodeId, item.operations, [...currentPath, item])
      if (result.length > 0) {
        return result
      }
    }
  }
  return []
}

// 获取节点的所有子节点（递归获取所有后代节点）
const getAllChildrenNodes = (node: any): any[] => {
  const children: any[] = []

  const traverse = (currentNode: any) => {
    if (currentNode.operations && currentNode.operations.length > 0) {
      for (const child of currentNode.operations) {
        children.push(child)
        traverse(child)
      }
    }
  }

  traverse(node)
  return children
}

// 查找节点对象（通过ID在整个数据树中查找）
const findNodeById = (nodeId: string, data: any[]): any => {
  for (const item of data) {
    if (item.id === nodeId) {
      return item
    }
    if (item.operations && item.operations.length > 0) {
      const found = findNodeById(nodeId, item.operations)
      if (found) {
        return found
      }
    }
  }
  return null
}

// 处理节点勾选逻辑
const handleNodeChecked = (nodeData: any) => {
  if (!permissionTreeRef.value || isUpdatingCheckState.value) return

  isUpdatingCheckState.value = true

  try {
    console.log(`勾选节点: ${nodeData.name} (ID: ${nodeData.id})`)

    // 向上传播：只勾选所有父节点，不勾选任何子节点
    const allParents = getAllParentNodes(nodeData.id, permissionTreeData.value)
    console.log(`向上传播，勾选 ${allParents.length} 个父节点:`, allParents.map(parent => parent.name))

    allParents.forEach(parent => {
      permissionTreeRef.value.setChecked(parent.id, true, false)
    })

    // 注意：不勾选任何子节点，只勾选当前节点本身和父节点
    console.log('不向下传播，保持子节点状态不变')

  } finally {
    isUpdatingCheckState.value = false
  }
}

// 处理节点取消勾选逻辑
const handleNodeUnchecked = (nodeData: any) => {
  if (!permissionTreeRef.value || isUpdatingCheckState.value) return

  isUpdatingCheckState.value = true

  try {
    console.log(`取消勾选节点: ${nodeData.name} (ID: ${nodeData.id})`)

    // 只向下传播：取消勾选所有子节点
    const allChildren = getAllChildrenNodes(nodeData)
    console.log(`向下传播，取消勾选 ${allChildren.length} 个子节点:`, allChildren.map(child => child.name))

    allChildren.forEach(child => {
      permissionTreeRef.value.setChecked(child.id, false, false)
    })

    // 不向上传播：不影响父节点的勾选状态
    console.log('不向上传播，保持父节点状态不变')

  } finally {
    isUpdatingCheckState.value = false
  }
}

// 节点勾选状态改变的处理函数
const handleNodeCheck = (
  data: any,
  checked: boolean,
  indeterminate: boolean
) => {
  // 防止在更新过程中触发递归调用
  if (isUpdatingCheckState.value) {
    return
  }

  const nodeLevel = getNodeLevel(data.id, permissionTreeData.value)

  console.log('=== 节点勾选状态改变 ===', {
    节点名称: data.name,
    节点ID: data.id,
    节点层级: `${nodeLevel}级`,
    勾选状态: checked ? '勾选' : '取消勾选',
    半选状态: indeterminate
  })

  if (checked) {
    handleNodeChecked(data)
  } else {
    handleNodeUnchecked(data)
  }

  console.log('=== 处理完成 ===\n')
}

// 表格选择变化处理
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 批量编辑角色
const handleBatchEdit = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要编辑的角色')
    return
  }

  // 执行编辑操作
  handleEdit(selectedRows.value[0])
}

// 批量删除角色
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的角色')
    return
  }

  ElMessageBox.alert(
    `是否确认删除以上角色信息?`,
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      // 用户点击确认，执行批量删除操作
      try {
        const ids = selectedRows.value.map((row: any) => row.id)
        const res: any = await roleManageApi.deleteRole({ ids })

        if (res?.code === 200 && res?.res_code === '000000') {
          ElMessage.success(res?.message || '删除角色成功')
          selectedRows.value = []
          // 刷新列表
          await getRoleList()
        } else {
          ElMessage.error(res?.message || '删除角色失败')
        }
      } catch (error) {
        console.error('删除角色失败:', error)
        ElMessage.error('删除角色失败，请稍后重试')
      }
    })
    .catch(() => {
      // 用户点击取消，不执行任何操作
      ElMessage.info('已取消删除')
    })
}

// 检查选择的角色中是否有不能删除的角色
const hasUnDeletableRoles = computed(() => {
  return selectedRows.value.some((row: any) => !row.type)
})

// 页面加载时获取角色列表
onMounted(() => {
  getRoleList()
})
</script>

<template>
  <PageMain>
    <div class="flex justify-between mb-4">
      <h2 class="text-lg font-bold">角色名称列表</h2>
      <div class="w-auto flex justify-between items-center">
        <el-button color="#00706B" @click="handleImport" v-auth="['roleandusermanage.rolemanage.import']">导入</el-button>
        <el-button color="#00706B" @click="handleExport" v-auth="['roleandusermanage.rolemanage.export']">导出</el-button>
        <el-button color="#00706B" @click="handleAdd" v-auth="['roleandusermanage.rolemanage.add']">新增</el-button>
        <el-button color="#00706B" @click="handleBatchEdit" v-auth="['roleandusermanage.rolemanage.edit']" :disabled="selectedRows?.length !== 1">编辑</el-button>
        <el-button color="#00706B" @click="handleBatchDelete" v-auth="['roleandusermanage.rolemanage.delete']" :disabled="!selectedRows?.length || hasUnDeletableRoles">删除</el-button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="my-4">
      <el-table
        :data="tableData"
        border
        size="small"
        class="table-height-query"
        v-loading="loading"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column align="center" prop="name" label="角色名称"></el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="120">
          <template #default="scope">
            <div class="flex justify-center flex-wrap">
              <!-- <el-button size="small" color="#00706B" @click="handleEdit(scope.row)" v-auth="['roleandusermanage.rolemanage.edit']">编辑</el-button> -->
              <el-button size="small" color="#00706B" @click="handlePermission(scope.row)" v-auth="['roleandusermanage.rolemanage.permoperate']">操作权限</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 翻页组件 -->
    <div class="flex justify-end">
      <el-pagination
        background
        layout="sizes, total, prev, pager, next"
        :page-size="pageSize"
        :page-sizes="[20, 30, 40, 50, 100]"
        :total="total"
        :page-count="pageCount"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      ></el-pagination>
    </div>

    <!-- 新增角色弹窗 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增角色名称"
      width="420"
      center
      destroy-on-close>
      <el-form
        :model="addForm"
        :rules="addRules"
        ref="addFormRef"
        label-position="left"
        label-width="90px">
        <el-form-item label="角色名称:" prop="name">
          <el-input
            v-model="addForm.name"
            placeholder="请输入角色名称"
            style="width: 100%;">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="handleAddSubmit">保存</el-button>
          <el-button @click="handleAddCancel">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑角色弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑角色名称"
      width="420"
      center
      destroy-on-close>
      <div class="original-role-display">
        <span class="original-role-label">原角色名称：</span>
        <span class="original-role-value">{{ editForm.originalName }}</span>
      </div>
      <el-form
        :model="editForm"
        :rules="editRules"
        ref="editFormRef"
        label-position="left"
        label-width="110px">
        <el-form-item label="新角色名称:" prop="newName">
          <el-input
            v-model="editForm.newName"
            placeholder="请输入新角色名称"
            style="width: 100%;">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="handleEditSubmit">保存</el-button>
          <el-button @click="handleEditCancel">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入角色弹窗 -->
    <el-dialog v-model="importDialogVisible" width="500" center align-center destroy-on-close>
      <div class="mb-4 flex flex-col items-center">
        <div class="flex items-center mb-4">
          <span class="mr-2">导入模板:</span>
          <el-button type="text" @click="downloadTemplate">点击下载角色信息导入模板</el-button>
        </div>

        <div class="flex items-center">
          <span class="mr-2">导入文件:</span>
          <div>
            <!-- 添加一个提示条件性显示 -->
            <div v-if="fileList.length > 0" class="mb-2 text-orange-500 text-xs">
              如需更换文件，请先点击文件右侧的"×"删除当前文件
            </div>
            <el-upload
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :limit="1"
              :file-list="fileList"
              :on-change="handleFileChange"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              accept=".xlsx,.xls"
            >
              <!-- 只有当fileList为空时才允许上传 -->
              <el-button color="#00706B" :disabled="fileList.length > 0">文件上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传xlsx/xls文件
                </div>
              </template>
            </el-upload>
          </div>
        </div>
      </div>
      <template #header="{ titleId }">
        <div :id="titleId" class="flex font-600 mb-6">导入</div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="confirmImport">确认</el-button>
          <el-button @click="cancelImport">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 操作权限弹窗 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="编辑角色操作权限信息"
      width="600"
      destroy-on-close
      :before-close="handlePermissionDialogClose">
      <div class="permission-content">
        <!-- 角色名称显示 -->
        <div class="role-info mb-4">
          <span class="role-label">角色名称：</span>
          <span class="role-value">{{ currentRoleForPermission?.name || '' }}</span>
        </div>

        <!-- 操作权限标签 -->
        <div class="permission-label mb-2">
          <span class="permission-title">操作权限：</span>
        </div>

        <!-- 权限树形控件容器 -->
        <div class="permission-tree-container" v-loading="permissionLoading">
          <!-- 权限数据为空时的提示 -->
          <div v-if="!permissionLoading && (!permissionTreeData || permissionTreeData.length === 0)"
               class="empty-permission-tip">
            <el-empty
              :image-size="80"
              description="暂无权限数据">
            </el-empty>
          </div>

          <!-- 权限树形控件 -->
          <el-tree
            v-else
            ref="permissionTreeRef"
            :data="permissionTreeData"
            show-checkbox
            node-key="id"
            :props="{
              children: 'operations',
              label: 'name'
            }"
            :default-expand-all="true"
            :check-strictly="true"
            @check-change="handleNodeCheck"
            class="permission-tree">
            <template #default="{ node, data }">
              <span class="tree-node-label">{{ data.name }}</span>
            </template>
          </el-tree>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button
            color="#00706B"
            @click="handlePermissionSave"
            :loading="permissionSaving"
            :disabled="permissionSaving || permissionLoading || !permissionTreeData.length">
            保存
          </el-button>
          <el-button @click="handlePermissionCancel" :disabled="permissionSaving">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </PageMain>
</template>

<style scoped>
/* 自定义样式 */
.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 原角色名称显示样式 */
.original-role-display {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.original-role-label {
  width: 110px; /* 与表单标签宽度一致 */
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.original-role-value {
  flex: 1;
  font-size: 14px;
  color: #909399;
}

/* 确保表单标签对齐 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  white-space: nowrap; /* 防止标签文本换行 */
}

/* 必填项星号颜色 */
:deep(.el-form-item.is-required .el-form-item__label::before) {
  color: #f56c6c;
}

/* 确保弹窗标题在左上角 */
:deep(.el-dialog__header) {
  text-align: left;

  /* padding: 16px 20px 0 20px; */
}

:deep(.el-dialog__title) {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
  color: #303133;
}

:deep(.el-dialog__body) {
  padding: 20px 20px 10px;
}

/* 操作权限弹窗样式 */
.permission-content {
  min-height: 400px;
}

.role-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.role-label {
  margin-right: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.role-value {
  font-size: 14px;
  color: #303133;
}

.permission-label {
  margin-bottom: 8px;
}

.permission-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.permission-tree-container {
  height: 300px;
  padding: 8px;
  overflow-y: auto;
  background-color: #fafafa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.empty-permission-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.permission-tree {
  background-color: transparent;
}

:deep(.el-tree-node__content) {
  height: 32px;
  line-height: 32px;
}

.tree-node-label {
  font-size: 14px;
  color: #606266;
}
</style>

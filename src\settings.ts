import { defaultsDeep } from 'lodash-es'
import type { RecursiveRequired, Settings } from '#/global'
import settingsDefault from '@/settings.default'

const globalSettings: Settings.all = {
  // 请在此处编写或粘贴配置代码
  menu: {
    style: 'line',
    isRounded: true,
    enableSubMenuCollapseButton: true,
    mode: 'single',
  },
  tabbar: {
    enable: false,
    style: '',
    enableIcon: false,
    dblclickAction: 'close',
    mergeTabsBy: '',
    enableMemory: false,
    enableHotkeys: false,
    storageTo: 'local',
  },
  toolbar: {
    navSearch: false,
    pageReload: false,
  },
  breadcrumb: {
    style: 'modern',
    enableMainMenu: true,
  },
  topbar: {
    mode: 'fixed',
  },
  home: {
    enable: false,
    title: 'route.home',
    fullPath: '/',
  },
  app: {
    lightTheme: 'light',
    storagePrefix: ''
  },
}

export default defaultsDeep(globalSettings, settingsDefault) as RecursiveRequired<Settings.all>


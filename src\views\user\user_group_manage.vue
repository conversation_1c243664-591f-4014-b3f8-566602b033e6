<template>
  <PageMain>
    <div class="user-group-container">
      <!-- 左侧树形结构 -->
      <div class="left-panel">
        <!-- <div class="tree-header">
          <h3>用户组列表</h3>
        </div> -->
        <div class="tree-content">
          <el-tree
            ref="treeRef"
            :data="treeData"
            :props="treeProps"
            :render-after-expand="false"
            node-key="id"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
            @node-contextmenu="handleRightClick"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <span class="node-label">{{ node.label }}</span>
                <div class="node-actions" v-if="data.type === 'unit'">
                  <el-button
                    size="small"
                    @click.stop="handleAddGroup(data)"
                    title="新增用户组"
                    v-auth="'roleandusermanage.usergroup.add'"
                  >
                    <SvgIcon name="i-ep:plus" class="icon" />
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-panel">
        <!-- 头部操作区 -->
        <!-- <div class="panel-header">
          <div class="header-title">
            <span v-if="currentUserGroup">{{ currentUserGroup.name }} </span>
            <span v-else>请选择用户组</span>
          </div>
        </div> -->
          <div class="header-actions" v-if="currentUserGroup">
            <el-button
              type="primary"
              @click="handleSelectUsers"
              color="#00706B"
              v-auth="'roleandusermanage.usergroup.selectjoingroup'"
            >
              用户组成员管理
            </el-button>
            <el-button
              type="success"
              @click="handleSelectWebsites"
              color="#00706B"
              v-auth="'roleandusermanage.usergroup.selectlookwebsiterange'"
            >
              可查看的网站范围管理
            </el-button>
          </div>
        <!-- 表格区域 -->
        <div class="table-content my-4" v-if="currentUserGroup">
          <el-table
            v-loading="loading"
            :data="userList"
            border
            class="table-height-query"
             size="small"
          >
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="name" label="用户名" min-width="120" />
            <el-table-column prop="real_name" label="姓名" min-width="100" />
            <el-table-column prop="belonging_unit" label="单位" min-width="150" />
            <el-table-column prop="phone" label="电话" min-width="150" />
            <el-table-column prop="email" label="邮箱" min-width="150" />
            <el-table-column prop="role" label="用户角色" min-width="120" />
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ row }">
                <el-button color="#00706B"
                  type="danger"
                  size="small"
                  @click="handleRemoveUser(row)"
                  v-auth="'roleandusermanage.usergroup.moveout'"
                >
                  移出
                </el-button>
              </template>
            </el-table-column>
          </el-table>
           <!-- 分页 -->
        <div class="flex justify-end m-t-4">
            <el-pagination
              background
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[20, 30, 40, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
            />
          </div>
        </div>
        <!-- 空状态 -->
        <div v-else class="empty-state">
          <el-empty description="请从左侧选择用户组" />
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-show="contextMenuVisible"
      class="context-menu"
      :style="{ left: contextMenuPosition.x + 'px', top: contextMenuPosition.y + 'px' }"
      @click="hideContextMenu"
    >
      <div class="menu-item" @click="handleRename" v-auth="'roleandusermanage.usergroup.rename'">
        <i class="ep:edit"></i>
        重命名
      </div>
      <div class="menu-item" @click="handleDelete" v-auth="'roleandusermanage.usergroup.delete'">
        <i class="ep:delete"></i>
        删除
      </div>
    </div>

    <!-- 新增/编辑用户组弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '重命名用户组' : '新增用户组'"
      width="480px"
      align-center
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="110px"
      >
        <!-- 所属单位名称回显 -->
        <el-form-item label="所属单位" v-if="!isEdit">
          <div class="unit-display">
            <span class="unit-name">{{ form.unit_name || '未选择单位' }}</span>
          </div>
        </el-form-item>
        <!-- 编辑时显示原用户组名称 -->
        <el-form-item v-if="isEdit" label="原用户组名">
          <span class="original-group-value">{{ form.original_name || '' }}</span>
        </el-form-item>
        <el-form-item label="用户组名称" prop="name">
          <el-input
            v-model="form.name"
            :placeholder="isEdit ? '请输入新的用户组名称' : '请输入用户组名称'"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
           <el-button color="#00706B" type="primary" @click="handleSubmit" :loading="submitLoading">
          确认
          </el-button>
          <el-button  @click="dialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户选择弹窗 -->
    <el-dialog
      v-model="userSelectDialogVisible"
      title="用户组成员管理"
      width="1000px"
      @close="resetUserSelection"
      @open="handleUserDialogOpen"
    >
      <div class="user-select-content">
        <!-- 查询条件 -->
        <div class="search-bar">
          <el-form :model="userSearchForm" inline>
            <el-form-item label="姓名">
              <el-input
                v-model="userSearchForm.real_name"
                placeholder="请输入姓名"
                style="width: 200px;"
                clearable
              />
            </el-form-item>
            <el-form-item label="单位">
              <el-tree-select
                style="width: 200px;"
                v-model="userSearchForm.unit_name"
                placeholder="请选择单位"
                filterable
                :props="{value: 'label'}"
                :data="unitOptions"
                :render-after-expand="false"
                clearable
                :disabled="role_name != 'SYSTEM_ADMIN'"
              >
              </el-tree-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                color="#00706B"
                @click="handleUserSearch"
              >
                查询
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 用户表格 -->
        <el-table
          ref="userTableRef"
          v-loading="userTableLoading"
          :data="allUsers"
          border
          height="400"
          :row-class-name="getRowClassName"
          @selection-change="handleUserSelectionChange"
        >
          <el-table-column
            type="selection"
            min-width="55"
            :selectable="(row) => !row.selected"
          />
          <el-table-column prop="name" label="用户名" min-width="120" />
          <el-table-column prop="real_name" label="姓名" min-width="100" />
          <el-table-column prop="belonging_unit" label="单位" min-width="180" />
          <el-table-column prop="phone" label="电话" min-width="130" />
          <el-table-column prop="email" label="邮箱" min-width="150" />
          <el-table-column prop="role" label="用户角色" min-width="120" />
        </el-table>

        <!-- 分页 -->
        <div class="flex justify-end mt-4">
          <el-pagination
            background
            v-model:current-page="userCurrentPage"
            v-model:page-size="userPageSize"
            :page-sizes="[20, 30, 40, 50, 100]"
            :total="userTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleUserSizeChange"
            @current-change="handleUserPageChange"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button color="#00706B" type="primary" @click="handleConfirmUsers" :loading="submitLoading">
            确认
          </el-button>
          <el-button @click="userSelectDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 网站选择弹窗 -->
    <el-dialog v-model="configDialogVisible" width="700" center align-center destroy-on-close>
      <div class="flex justify-center">
        <el-transfer
          v-model="selectedNetworkKeys"
          :data="allNetworks"
          :titles="['所有网站名称', '已分配的网站']"
        />
      </div>
      <template #header="{ titleId }">
        <div :id="titleId" class="flex font-600 mb-6">选择可查看的网站范围</div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="saveConfig" :loading="loading">保存</el-button>
          <el-button @click="cancelConfig">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </PageMain>
</template>

<script setup lang="ts">
import { nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import userGroupApi from '@/api/modules/userGroup'
import storage from '@/utils/storage'
import useBusinessStore from '@/store/modules/business'


// 类型定义
interface TreeNode {
  id: string
  name: string
  type: 'unit' | 'userGroup'
  unit_id?: string
  unit_name?: string
  disabled?: boolean
  children?: TreeNode[]
}
const role_name =ref(storage.local.get('role')||'');

// 业务数据store
const businessStore = useBusinessStore()

// 树形数据
const treeData = ref<TreeNode[]>([])
const treeProps = {
  children: 'children',
  label: 'name'
}
const treeRef = ref()

// 当前选中的用户组
const currentUserGroup = ref<TreeNode | null>(null)
const rightClickNode = ref<TreeNode | null>(null)

// 用户列表
const userList = ref([])
const loading = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 弹窗状态
const dialogVisible = ref(false)
const userSelectDialogVisible = ref(false)
const configDialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)

// 表单数据
const form = reactive({
  id: '',
  name: '',
  unit_id: '',
  unit_name: '',
  original_name: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入用户组名称', trigger: 'blur' },
    { min: 1, max: 50, message: '用户组名称长度在1-50个字符', trigger: 'blur' }
  ]
}

const formRef = ref()

// 右键菜单
const contextMenuVisible = ref(false)
const contextMenuPosition = reactive({ x: 0, y: 0 })

// 用户选择相关
const allUsers = ref([])
const originalUserIds = ref([])
const userTableRef = ref()

// 用户选择弹窗查询条件
const userSearchForm = reactive({
  real_name: '',
  unit_name: ''
})

// 单位列表数据
const unitOptions = ref<any[]>([])

const userCurrentPage = ref(1)
const userPageSize = ref(20)
const userTotal = ref(0)
const userTableLoading = ref(false)
const selectedUserRows: Ref<any[]> = ref([])

// 网站选择相关
const allNetworks = ref([])
const selectedNetworkKeys = ref([])
const originalWebsiteIds = ref([])

// 初始化
onMounted(() => {
  initData()
  document.addEventListener('click', hideContextMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', hideContextMenu)
})

// 初始化数据
const initData = async () => {
  await loadTreeData()
  // await businessStore.getUnits() // 加载单位数据
}

// 加载树形数据
const loadTreeData = async () => {
  try {
    const res: any = await userGroupApi.getUserGroups()
    if (res?.code == 200) {
      treeData.value = res.data?.map((unit: any) => ({
        id: unit.unit_id,
        name: unit.unit_name,
        type: 'unit' as const,
        disabled: true, // 设置一级数据不可选中
        children: unit.user_groups?.map((group: any) => ({
          id: group.id,
          name: group.name,
          type: 'userGroup' as const,
          unit_id: unit.unit_id,
          unit_name: unit.unit_name
        }))
      }))

      // 自动选中第一个用户组
      await autoSelectFirstUserGroup()
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    // ElMessage.error('加载数据失败')
  }
}

// 自动选中第一个用户组
const autoSelectFirstUserGroup = async () => {
  // 等待DOM更新
  await nextTick()

  // 查找第一个用户组
  for (const unit of treeData.value) {
    if (unit.children && unit.children.length > 0) {
      const firstUserGroup = unit.children[0]
      currentUserGroup.value = firstUserGroup

      // 设置树形控件的当前选中项
      if (treeRef.value) {
        treeRef.value.setCurrentKey(firstUserGroup.id)
      }

      // 加载用户组成员数据
      await loadUserGroupUsers()
      break
    }
  }
}

// 节点点击事件
const handleNodeClick = (data: TreeNode) => {
  // 只有用户组类型且不是禁用状态才可以点击
  if (data.type === 'userGroup' && !data.disabled) {
    currentUserGroup.value = data
    loadUserGroupUsers()
  }
  // 一级数据（单位）不可点击，不做任何处理
}

// 加载用户组成员
const loadUserGroupUsers = async () => {
  if (!currentUserGroup.value) return

  loading.value = true
  try {
    const res: any = await userGroupApi.getUserGroupUsers(currentUserGroup.value.id, {
      page_index: currentPage.value,
      page_size: pageSize.value,
      selected_flag: '1'
    })

    if (res?.code == 200) {
      userList.value = res.data.page_list || []
      total.value = res.data.total_num || 0
    }
  } catch (error) {
    console.error('加载用户组成员失败:', error)
    // ElMessage.error('加载用户组成员失败')
  } finally {
    loading.value = false
  }
}

// 分页处理
const handlePageChange = () => {
  loadUserGroupUsers()
}

const handleSizeChange = () => {
  currentPage.value = 1
  loadUserGroupUsers()
}

// 新增用户组
const handleAddGroup = (unitData: TreeNode) => {
  isEdit.value = false
  form.name = ''
  form.unit_id = unitData.id
  form.unit_name = unitData.name
  form.original_name = ''
  dialogVisible.value = true
}

// 右键菜单
const handleRightClick = (event: MouseEvent, data: TreeNode) => {
  if (data.type === 'userGroup') {
    event.preventDefault()
    rightClickNode.value = data
    contextMenuPosition.x = event.clientX
    contextMenuPosition.y = event.clientY
    contextMenuVisible.value = true
  }
}

const hideContextMenu = () => {
  contextMenuVisible.value = false
}

// 重命名
const handleRename = () => {
  hideContextMenu()
  if (rightClickNode.value) {
    isEdit.value = true
    form.id = rightClickNode.value.id
    form.name = rightClickNode.value.name
    form.unit_id = rightClickNode.value.unit_id || ''
    form.unit_name = rightClickNode.value.unit_name || ''
    form.original_name = rightClickNode.value.name
    dialogVisible.value = true
  }
}

// 删除用户组
const handleDelete = async () => {
  hideContextMenu()
  if (!rightClickNode.value) return
  try {
    await ElMessageBox.confirm(
      '是否确认删除此条用户组信息？用户组内相关信息将同步删除!',
      '确认删除',
      {
        confirmButtonText: '确认',
        type: 'warning'
      }
    )

    const res: any = await userGroupApi.deleteUserGroup({
      ids: [rightClickNode.value.id]
    })

    if (res.code === 200) {
      ElMessage.success('删除成功')
      await loadTreeData()
      // 如果删除的是当前选中的用户组，清空右侧内容
      if (currentUserGroup.value?.id === rightClickNode.value.id) {
        currentUserGroup.value = null
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      // ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true
    if (isEdit.value) {
      // 编辑
      const res: any = await userGroupApi.updateUserGroup({
        id: form.id,
        name: form.name
      })
      if (res.code === 200) {
        ElMessage.success('重命名成功')
        dialogVisible.value = false
        await loadTreeData()
      }
    } else {
      // 新增
      const res: any = await userGroupApi.addUserGroup({
        unit_id: form.unit_id,
        datalist: [{ name: form.name }]
      })

      if (res.code === 200) {
        ElMessage.success('新增成功')
        dialogVisible.value = false
        await loadTreeData()
      }
    }
  } catch (error) {
    console.error('操作失败:', error)
    // ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.id = ''
  form.name = ''
  form.unit_id = ''
  form.unit_name = ''
  form.original_name = ''
}

// 选择用户
const handleSelectUsers = async () => {
  if (!currentUserGroup.value) return

  try {
    // 重置查询条件和分页
    userSearchForm.real_name = ''
    userSearchForm.unit_name = ''
    userCurrentPage.value = 1
    userPageSize.value = 20

    // 加载用户数据
    await loadUserSelectData()

    userSelectDialogVisible.value = true
  } catch (error) {
    console.error('加载用户数据失败:', error)
    // ElMessage.error('加载用户数据失败')
  }
}

// 处理用户选择弹窗打开
const handleUserDialogOpen = async () => {
  // 加载单位数据
  await loadUnitOptions()

  // 弹窗打开时设置默认选中状态
  nextTick(() => {
    setDefaultSelection()
  })
}

// 获取表格行类名
const getRowClassName = ({ row }: { row: any }) => {
  return row.selected ? 'user-selected-row' : ''
}

// 加载用户选择数据
const loadUserSelectData = async () => {
  userTableLoading.value = true
  try {
    // 获取所有用户数据
    const allUsersRes: any = await userGroupApi.getUserGroupUsers(currentUserGroup.value?.id || '', {
      page_index: userCurrentPage.value,
      page_size: userPageSize.value,
     ...userSearchForm
    })

    if (allUsersRes) {
      allUsers.value = allUsersRes.data.page_list || []
      userTotal.value = allUsersRes?.data.total_num || 0

      // 等待DOM更新后设置默认选中
      await nextTick()
      setDefaultSelection()
    }

  } catch (error) {
    console.error('加载用户数据失败:', error)
    // ElMessage.error('加载用户数据失败')
  } finally {
    userTableLoading.value = false
  }
}

// 设置默认选中状态
const setDefaultSelection = () => {
  if (!userTableRef.value) return

  // 清空当前选中
  userTableRef.value.clearSelection()

  // 选中所有 selected 为 true 的行（已加入的用户）
  allUsers.value.forEach((user: any) => {
    if (user.selected) {
      userTableRef.value.toggleRowSelection(user, true)
    }
  })
}

// 用户查询
const handleUserSearch = () => {
  userCurrentPage.value = 1
  loadUserSelectData()
}

// 用户表格选择变化
const handleUserSelectionChange = (selectedRows: any[]) => {
  selectedUserRows.value = selectedRows
}

// 用户分页处理
const handleUserPageChange = () => {
  loadUserSelectData()
}

const handleUserSizeChange = () => {
  userCurrentPage.value = 1
  loadUserSelectData()
}

// 确认用户选择
const handleConfirmUsers = async () => {
  if (!currentUserGroup.value) return

  try {
    submitLoading.value = true
    // 获取当前选中的用户ID
    const newSelectedUserIds = selectedUserRows.value?.map((user: any) => user.id)
    
    // 计算需要新增和删除的用户
    const addUserIds = newSelectedUserIds.filter((id: string) => !originalUserIds.value.includes(id as never))
    const delUserIds = originalUserIds.value.filter((id: string) => !newSelectedUserIds.includes(id as never))

    const res: any = await userGroupApi.updateUserGroupUsers(currentUserGroup.value.id, {
      add_user_ids: addUserIds,
      del_user_ids: delUserIds
    })

    if (res?.code == 200) {
      ElMessage.success('用户设置成功')
      userSelectDialogVisible.value = false
      await loadUserGroupUsers()
    } else {
      ElMessage.error(res.data?.message || '用户设置失败')
    }
  } catch (error) {
    console.error('设置用户失败:', error)
    ElMessage.error('设置用户失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置用户选择
const resetUserSelection = () => {
  selectedUserRows.value = []
  originalUserIds.value = []
  allUsers.value = []
  userSearchForm.real_name = ''
  userSearchForm.unit_name = ''
  userCurrentPage.value = 1
  userPageSize.value = 20
  userTotal.value = 0
  unitOptions.value = []
}

// 移出用户
const handleRemoveUser = async (user: any) => {
  if (!currentUserGroup.value) return

  try {
    await ElMessageBox.confirm(
      `确定要将用户"${user.real_name}"移出该用户组吗？`,
      '确认移出',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const res: any = await userGroupApi.updateUserGroupUsers(currentUserGroup.value.id, {
      add_user_ids: [],
      del_user_ids: [user.id]
    })

    if (res.code === 200) {
      ElMessage.success('移出成功')
      await loadUserGroupUsers()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移出失败:', error)
      ElMessage.error('移出失败')
    }
  }
}

// 选择网站
const handleSelectWebsites = async () => {
  if (!currentUserGroup.value) return
  try {
    configDialogVisible.value = true
    // 获取所有网站
    const allNetworksRes: any = await userGroupApi.getUserGroupNetworks(currentUserGroup.value.id, '0')
    // 获取当前用户组已分配的网站
    const groupWebsitesRes: any = await userGroupApi.getUserGroupNetworks(currentUserGroup.value.id, '1')
    if (allNetworksRes) {
      console.log(allNetworksRes.data)
      let allNetworkArr = allNetworksRes.data.concat(groupWebsitesRes.data) 
      allNetworks.value = allNetworkArr?.map((website: any) => ({
        key: website.id,
        label: `${website.network_name} (${website.network_ip})`,
        disabled: false
      }))
    }
    // 设置已选择的网站
    selectedNetworkKeys.value = groupWebsitesRes.data?.map((item: any) => item.id)
  } catch (error) {
    console.error('加载网站数据失败:', error)
    // ElMessage.error('加载网站数据失败')
  }
}

// 确认网站选择
const saveConfig = async () => {
  if (!currentUserGroup.value) return

  try {
    submitLoading.value = true

  // 获取当前已分配的网站
   const assignedNetworks = await userGroupApi.getUserGroupNetworks(currentUserGroup.value.id, '1')
    const oldKeys = assignedNetworks.data?.map((item: any) => item.id)

    // 计算需要新增和删除的网站
    const toAdd = selectedNetworkKeys.value.filter((key: any) => !oldKeys.includes(key))
    const toRemove = oldKeys.filter((key: never) => !selectedNetworkKeys.value.includes(key))

    let addSuccess: any = true
    let removeSuccess: any = true

    // 添加新分配的网站
    if (toAdd.length > 0) {
        addSuccess = await userGroupApi.addNetworksToUserGroup(currentUserGroup.value.id, {
        network_info_ids: toAdd
      })
    }

    // 删除取消分配的网站
    if (toRemove.length > 0) {
      removeSuccess = await userGroupApi.removeNetworksFromUserGroup(currentUserGroup.value.id, {
        network_info_ids: toRemove
      })
    }

    if (addSuccess && removeSuccess) {
      configDialogVisible.value = false
      ElMessage.success('网站权限设置成功')
    }
  } catch (error) {
    console.error('设置网站权限失败:', error)
    ElMessage.error('设置网站权限失败')
  } finally {
    submitLoading.value = false
  }
}

  // 重置网站选择
  const resetWebsiteSelection = () => {
    selectedNetworkKeys.value = []
    originalWebsiteIds.value = []
    allNetworks.value = []
  }

  // 取消权限配置
  const cancelConfig = () => {
    configDialogVisible.value = false
    resetWebsiteSelection()
  }

// 加载单位选项数据
const loadUnitOptions = async () => {
  try {
    // 使用业务store获取单位数据
    await businessStore.getUnitNameList()
    unitOptions.value = businessStore.unitNameOriginalList

    // 如果不是系统管理员，默认选中第一条数据并搜索
    if (role_name.value != 'SYSTEM_ADMIN' && unitOptions.value.length > 0) {
      userSearchForm.unit_name = unitOptions.value[0].label
      // 触发搜索
      await handleUserSearch()
    }
  } catch (error) {
    console.error('加载单位数据失败:', error)
  }
}
</script>

<style scoped>
.user-group-container {
  display: flex;
  overflow: hidden;
  background: white;
  border-radius: 8px;
}

.left-panel {
  display: flex;
  flex-direction: column;
  width: 400px;
  height: calc(100vh - 148px);
  border-right: 1px solid #e4e7ed;
}

.tree-header {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.tree-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tree-content {
  flex: 1;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}

.node-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-actions {
  display: flex;
  gap: 4px;
}

.disabled-text {
  color: #909399;
}

.right-panel {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
  margin: 16px 0 0 16px;
}

.table-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 0 16px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.empty-state {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
}

.context-menu {
  position: fixed;
  z-index: 1000;
  min-width: 120px;
  padding: 4px 0;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgb(0 0 0 / 10%);
}

.menu-item {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 16px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
}

.menu-item:hover {
  color: #409eff;
  background: #f5f7fa;
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.user-select-content {
  padding: 16px 0;
}

.search-bar {
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 6px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  color: #1890ff;
  background-color: #e6f7ff;
}

/* 禁用状态的树节点样式 */
:deep(.el-tree-node[data-type="unit"] .el-tree-node__content) {
  color: #909399;
  cursor: not-allowed;
}

:deep(.el-tree-node[data-type="unit"] .el-tree-node__content:hover) {
  background-color: transparent !important;
}

:deep(.el-transfer__buttons) {
  padding: 0 30px;
}

</style>
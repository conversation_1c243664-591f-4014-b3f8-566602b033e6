<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import Tools from './tools.vue'
import eventBus from '@/utils/eventBus'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'

defineOptions({
  name: 'ToolbarRightSide',
})

const router = useRouter()

const settingsStore = useSettingsStore()
const userStore = useUserStore()

const { t } = useI18n()
const { generateI18nTitle } = useMenu()

const avatarError = ref(false)
watch(() => userStore.avatar, () => {
  if (avatarError.value) {
    avatarError.value = false
  }
})
</script>

<template>
  <div class="flex items-center">
    <Tools mode="right-side" />
    <div class="flex-center gap-1">
      <img v-if="userStore.avatar && !avatarError" :src="userStore.avatar" :onerror="() => (avatarError = true)" class="h-[24px] w-[24px] rounded-full">
      <SvgIcon v-else name="i-carbon:user-avatar-filled-alt" :size="32" class="text-gray-400" />
      <div>
        <div class="text-sm">{{ userStore.chinese_name }}</div>
        <div class="text-xs">{{ userStore.unit_name }}</div>
      </div>
      <!-- <SvgIcon name="i-ep:caret-bottom" /> -->
    </div>
    <div class="ml-4 cursor-pointer" @click="userStore.logout(settingsStore.settings.home.fullPath)">
      {{ t('app.logout') }}
    </div>
  </div>
</template>

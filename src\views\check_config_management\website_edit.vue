<route lang="yaml">
  meta:
    title: 网站栏目编辑
  </route>

<script setup lang="ts">
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import websiteManageApi from '@/api/modules/websiteManagement'
import { nextTick } from 'vue'
import importOrExportApi from '@/api/modules/importOrExport'
import { extractFilenameFromResponse, saveFileWithPicker } from '@/utils'
type tableDataItem = {
  column_type?: string
  first_column?: string
  second_column?: string
  third_column?: string
  ip_address?: string
  network_info_id?: string
  timeliness_rule_type: string
  timeliness_rule_count: number
  timeliness_id?: string
  id?: string
  _isNew?: boolean // 标记是否为新增行
  _isEdited?: boolean // 标记是否被编辑过
  _isNewEditing?: boolean // 标记是否为正在编辑中的新行
}

const { queryColumnData, exportWebsiteColumn, deleteColumnData, importWebsiteColumn, addOrUpdateColumnData } = websiteManageApi
const { downloadColumnTemplate } = importOrExportApi

// 数据
const tableData = ref<tableDataItem[]>([])
const tableRef = ref()
const isSaving = ref(false) // 添加保存状态标记
// 添加选中行数据存储
const selectedRows = ref<tableDataItem[]>([])
// 编辑模式状态
const isEditMode = ref(false)
// 添加待保存的新增行跟踪
const pendingNewRows = ref<tableDataItem[]>([])

// 编辑弹窗相关状态
const dialogVisible = ref(false)
const formLabelWidth = '120px'
const formRef = ref()
const form = reactive({
  column_type: '',
  first_column: '',
  second_column: '',
  third_column: '',
  ip_address: '',
  timeliness_rule_type: 'DAYS',
  timeliness_rule_count: 1,
  id: '',
  network_info_id: '',
  timeliness_id: ''
})

// 域名/IP地址格式校验函数
  // 域名/IP地址格式校验函数
  const validateDomainOrIP = (value: string) => {
    if (!value) return false

    // IP格式：1-3位数字.1-3位数字.1-3位数字.1-3位数字
    const ipRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

    // IP+路径格式
    const ipPathRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // IP+端口号格式：IP地址:1-5位数字(端口号)
    const ipPortRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}$/

    // IP+端口号+路径格式
    const ipPortPathRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 域名格式（支持中英文域名）
    const domainRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])$/

    // 域名+路径格式
    const domainPathRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 域名+端口号格式
    const domainPortRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}$/

    // 域名+端口号+路径格式
    const domainPortPathRegex = /^(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 带协议的URL格式（http://或https://开头）
    const urlRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])$/

    // 带协议的URL+路径格式
    const urlPathRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5])(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 带协议的URL+端口号格式
    const urlPortRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}$/

    // 带协议的URL+端口号+路径格式
    const urlPortPathRegex = /^(https?:\/\/)(([a-zA-Z0-9\u4e00-\u9fa5]|[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\-]*[a-zA-Z0-9\u4e00-\u9fa5])\.)+([A-Za-z\u4e00-\u9fa5]|[A-Za-z\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\-]*[A-Za-z0-9\u4e00-\u9fa5]):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 带协议的IP地址URL格式（http://或https://开头）
    const ipUrlRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/

    // 带协议的IP地址URL+路径格式
    const ipUrlPathRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    // 带协议的IP地址URL+端口号格式
    const ipUrlPortRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}$/

    // 带协议的IP地址URL+端口号+路径格式
    const ipUrlPortPathRegex = /^(https?:\/\/)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5}(\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)$/

    return ipRegex.test(value) ||
            ipPathRegex.test(value) ||
            ipPortRegex.test(value) ||
            ipPortPathRegex.test(value) ||
            domainRegex.test(value) ||
            domainPathRegex.test(value) ||
            domainPortRegex.test(value) ||
            domainPortPathRegex.test(value) ||
            urlRegex.test(value) ||
            urlPathRegex.test(value) ||
            urlPortRegex.test(value) ||
            urlPortPathRegex.test(value) ||
            ipUrlRegex.test(value) ||
            ipUrlPathRegex.test(value) ||
            ipUrlPortRegex.test(value) ||
            ipUrlPortPathRegex.test(value)
  }

// 表单验证规则
const rules = reactive({
  column_type: [{ required: true, message: '请输入栏目类型', trigger: 'blur' }],
  first_column: [{ required: true, message: '请输入一级栏目', trigger: 'blur' }],
  ip_address: [
    { required: true, message: '请输入域名/IP', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback(new Error('请输入域名/IP'))
        } else if (!validateDomainOrIP(value)) {
          callback(new Error('请输入有效的域名/IP地址格式'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  timeliness_rule_type: [{ required: true, message: '请选择及时性要求周期', trigger: 'change' }],
  timeliness_rule_count: [{ required: true, message: '请输入及时性要求次数', trigger: 'blur' }]
})

const router = useRouter()
const route = useRoute()

const uploadFileList = ref([])

// 导入弹窗显示控制
const importDialogVisible = ref(false)
// 导入文件相关
const uploadFile = ref(null)
const fileList = ref([])

// 分页相关
const total = ref(0)
const currentPage = ref(1)
const pageCount = ref(0)
const pageSize = ref(20)

onMounted(async () => {
  await getWebsiteColumnData(route.query.websiteId as string)
})

const getWebsiteColumnData = async (id: string) => {
  try {
    const res: any = await queryColumnData({
      network_id: id,
      page_index: currentPage.value,
      page_size: pageSize.value
  })
  if (res?.code === 200 && res?.res_code === '000000') {
      tableData.value = res?.data.page_list || []
      total.value = res?.data.total_num || 0
      pageCount.value = res?.data.page_num || 0
      console.log('获取网站栏目详情数据', res)

      // 清空待保存的新增行
      pendingNewRows.value = []
    } else {
      ElMessage.warning(res.message || '获取网站栏目详情数据失败')
    }
  } catch (error) {
    console.error('获取网站栏目详情数据失败:', error)
    ElMessage.error('获取网站栏目详情数据失败')
  }
}

// 修改handleCheckboxAll函数，允许多选
const handleCheckboxAll = ({ checked, $event }: any) => {
  const $table = tableRef.value
  const rows = $table.getCheckboxRecords()
  // 更新选中行数据
  if (checked) {
    selectedRows.value = [...rows]
  } else {
    selectedRows.value = []
  }
}

// 修改单选处理函数，允许多选
const handleCheckboxChange = ({ checked, row }: { checked: boolean, row: tableDataItem }) => {
  console.log('选中行变化:', checked, row)

  if (checked) {
    // 添加到选中行
    selectedRows.value.push(row)
  } else {
    // 从选中行中移除
    const index = selectedRows.value.findIndex(item => item === row)
    if (index !== -1) {
      selectedRows.value.splice(index, 1)
    }
  }
}

const timelineName = (ruleType: string) => {
  if (ruleType === 'DAYS') return '自然日'
  if (ruleType === 'WORK_DAYS') return '工作日'
  if (ruleType === 'YEARS') return '年'
  if (ruleType === 'WEEKS') return '周'
  if (ruleType === 'MONTHS') return '月'
  if (ruleType === 'QUARTERS') return '季度'
}

const timelineNameGropus = ref([
  { label: '年', value: 'YEARS' },
  { label: '月', value: 'MONTHS' },
  { label: '季度', value: 'QUARTERS' },
  { label: '周', value: 'WEEKS' },
  { label: '自然日', value: 'DAYS' },
  { label: '工作日', value: 'WORK_DAYS' },
])

// VxeTable编辑相关事件处理
const handleEditActived = ({ row }: { row: tableDataItem }) => {
  console.log('开始编辑行:', row)

  // 如果是新增行且还未添加到待保存列表，则立即添加
  if (row._isNew) {
    // 确保网站ID存在
    row.network_info_id = route.query.websiteId as string

    // 检查是否已经在待保存列表中，避免重复添加
    const existingIndex = pendingNewRows.value.findIndex(item => item === row)
    if (existingIndex === -1) {
      pendingNewRows.value.push(row)
      console.log('新增行开始编辑，已添加到待保存列表', pendingNewRows.value.length)
    }
  }
}

// 修改编辑完成时的处理函数
const handleEditClosed = async ({ row, $table }: { row: tableDataItem, $table: any }) => {
  if (isSaving.value) return // 避免重复保存

  // 域名/IP格式校验
  if (row.ip_address && !validateDomainOrIP(row.ip_address)) {
    ElMessage.warning('请输入有效的域名/IP地址格式')
    // 恢复编辑状态，防止关闭编辑状态
    await $table.setActiveRow(row)
    return
  }

  // 标记行已被编辑
  row._isEdited = true

  // 编辑完成后，移除新行编辑状态标记
  if (row._isNewEditing) {
    row._isNewEditing = false
  }

  // 如果是新增行，将其添加到待保存列表中
  if (row._isNew) {
    // 确保网站ID存在
    row.network_info_id = route.query.websiteId as string

    // 检查是否已经在待保存列表中
    const existingIndex = pendingNewRows.value.findIndex(item => item === row)
    if (existingIndex === -1) {
      pendingNewRows.value.push(row)
    }

    console.log('新增行待保存，将在点击保存按钮时提交', pendingNewRows.value.length)
    return
  }
}

// 打开编辑弹窗
const openEditDialog = () => {
  if (selectedRows.value.length !== 1) {
    ElMessage.warning('请选择一行数据进行编辑')
    return
  }

  const row = selectedRows.value[0]

  // 将选中行数据填充到表单
  form.column_type = row.column_type || ''
  form.first_column = row.first_column || ''
  form.second_column = row.second_column || ''
  form.third_column = row.third_column || ''
  form.ip_address = row.ip_address || ''
  form.timeliness_rule_type = row.timeliness_rule_type
  form.timeliness_rule_count = row.timeliness_rule_count
  form.id = row.id || ''
  form.network_info_id = row.network_info_id || (route.query.websiteId as string)
  form.timeliness_id = row.timeliness_id || ''

  dialogVisible.value = true
}

// 处理弹窗保存
const handleSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        isSaving.value = true

        // 显示加载状态
        const loadingInstance = ElLoading.service({
          lock: true,
          text: '正在保存...',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        try {
          // 构建要保存的数据
          const saveData = {
            columns: [{
              column_type: form.column_type,
              first_column: form.first_column,
              second_column: form.second_column,
              third_column: form.third_column,
              ip_address: form.ip_address,
              timeliness_rule_type: form.timeliness_rule_type,
              timeliness_rule_count: form.timeliness_rule_count,
              id: form.id,
              network_info_id: form.network_info_id || (route.query.websiteId as string),
              timeliness_id: form.timeliness_id
            }]
          }

          // 调用保存API
          const res: any = await addOrUpdateColumnData(saveData)

          if (res.code === 200 && res?.res_code !== '000000') {
            ElMessage.warning(res.message)
          } else if (res.code === 200 && res.res_code === '000000') {
            // 更新成功
            ElMessage.success('更新成功')
            dialogVisible.value = false

            // 重新获取数据，确保ID等字段更新
            await getWebsiteColumnData(route.query.websiteId as string)

            // 清空选中行
            selectedRows.value = []
            const $table = tableRef.value
            if ($table) {
              $table.clearCheckboxRow()
            }
          } else {
            ElMessage.error(res.message || '保存失败')
          }
        } finally {
          // 关闭加载提示
          loadingInstance.close()
          isSaving.value = false
        }
      } catch (error) {
        console.error('保存行数据失败:', error)
        ElMessage.error('保存失败，请重试')
        isSaving.value = false
      }
    } else {
      ElMessage.error('请完善表单信息')
    }
  })
}

// 下载及时性规则编辑模板
const downloadTemplate = async () => {
  try {
    const res: any = await downloadColumnTemplate()
    const filename = extractFilenameFromResponse(res, '及时性规则编辑模板.xlsx')

    // 调用优化后的saveFileWithPicker
    const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    if (saveResult === true) {
      // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
    } else if (saveResult === 'default') {
      // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
    } else {
      // 用户取消操作
      ElMessage.info('模板下载已取消')
    }
  } catch(error) {
    console.error('下载模板失败:', error)
    ElMessage.error('模板下载失败，请稍后重试')
  }
}

// 打开导入弹窗
const openImportDialog = () => {
  importDialogVisible.value = true
  fileList.value = []
  uploadFile.value = null
}

// 文件上传前的验证
const beforeUpload = (file: any) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件(格式为xlsx或xls)')
    return false
  }
  return true
}

// 文件变化处理
const handleFileChange = (file: any, uploadFileList: any) => {
  if (file.status === 'ready') {
    uploadFile.value = file.raw
    fileList.value = uploadFileList
  }
}

// 文件移除处理
const handleRemove = () => {
  uploadFile.value = null
  fileList.value = []
}

// 确认导入
const confirmImport = async () => {
  if (!uploadFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  try {
    const formData = new FormData()
    formData.append('file', uploadFile.value)

    // 显示加载提示
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '文件上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      const res: any = await importWebsiteColumn(route.query.websiteId as string, formData)

      if (res.code === 200 && res.res_code !== '000000') {
        ElMessage.warning(res.message)
      } else if (res.code === 200 && res.res_code === '000000') {
        ElMessage.success('导入成功')
        importDialogVisible.value = false
        fileList.value = []
        uploadFile.value = null
        await getWebsiteColumnData(route.query.websiteId as string)
      } else {
        ElMessage.error(res.message || '导入失败')
      }
    } catch (error) {
      console.error('上传处理错误:', error)
      ElMessage.error('文件上传失败，请检查文件格式是否正确')
    } finally {
      // 关闭加载提示
      loadingInstance.close()
    }
  } catch(error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请稍后重试')
  }
}

// 取消导入
const cancelImport = () => {
  importDialogVisible.value = false
  uploadFile.value = null
  fileList.value = []
}

// 修改新增行方法
const handleAddNewLine = async () => {
  const $table = tableRef.value
  if ($table) {
    // 清除所有选中行
    $table.clearCheckboxRow()
    selectedRows.value = []

    const record = {
      column_type: '',
      first_column: '',
      second_column: '',
      third_column: '',
      ip_address: '',
      network_info_id: route.query.websiteId as string,
      timeliness_id: '',
      timeliness_rule_type: 'DAYS',
      timeliness_rule_count: 1,
      _isNew: true, // 标记为新行
      _isEdited: false, // 初始未编辑
      _isNewEditing: true // 特殊标记，表示这是正在编辑中的新行
    }

    tableData.value.unshift(record as never)
    const { row: newRow } = await $table.insertAt(record, 0)

    // 立即进入编辑状态
    await $table.setActiveRow(newRow)
  }
}

const handleDownload = async () => {
  try {
  const res: any = await exportWebsiteColumn(route.query.websiteId as string)
  const filename = extractFilenameFromResponse(res, `${route.query.networkName}${route.query.networkType}网站专栏设置管理一览表.xlsx`)

    // 调用优化后的saveFileWithPicker
    const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    if (saveResult === true) {
      // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
    } else if (saveResult === 'default') {
      // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
    } else {
      // 用户取消操作
      ElMessage.info('导出已取消')
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

const handleCancelEdit = () => {
  router.replace({ name: 'websiteColumnManagement' })
}

// 修改保存按钮逻辑，保存所有新增和修改的行
const handleSave = async () => {
  if (isSaving.value) return // 避免重复保存

  if (pendingNewRows.value.length === 0) {
    ElMessage.info('没有需要保存的数据')
    return
  }

  try {
    isSaving.value = true

    // 显示加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在保存...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 验证所有待保存行的数据有效性（增加域名/IP格式校验）
      const invalidEmptyRows = pendingNewRows.value.filter(row => !row.ip_address)
      if (invalidEmptyRows.length > 0) {
        ElMessage.warning(`有 ${invalidEmptyRows.length} 行数据不完整，请确保域名/IP已填写`)
        loadingInstance.close()
        isSaving.value = false
        return
      }

      // 验证域名/IP格式
      const invalidFormatRows = pendingNewRows.value.filter(row => !validateDomainOrIP(row.ip_address || ''))
      if (invalidFormatRows.length > 0) {
        ElMessage.warning(`有 ${invalidFormatRows.length} 行数据的域名/IP格式无效，请检查`)
        loadingInstance.close()
        isSaving.value = false
        return
      }

      // 构建要保存的数据
      const saveData = {
        columns: pendingNewRows.value.map(row => ({
          ...row,
          network_info_id: route.query.websiteId as string
        }))
      }

      console.log('即将保存的数据:', saveData)

      // 调用批量保存API
      const res: any = await addOrUpdateColumnData(saveData)

      if (res.code === 200 && res.res_code !== '000000') {
        ElMessage.warning(res.message)
      } else if (res.code === 200 && res.res_code === '000000') {
        // 保存成功
        ElMessage.success(`成功保存 ${pendingNewRows.value.length} 条数据`)

        // 重新获取数据，确保ID等字段更新
        await getWebsiteColumnData(route.query.websiteId as string)
      } else {
        ElMessage.error(res.message || '保存失败')
      }
    } finally {
      // 关闭加载提示
      loadingInstance.close()
      isSaving.value = false
    }
  } catch (error) {
    console.error('保存数据失败:', error)
    ElMessage.error('保存失败，请重试')
    isSaving.value = false
  }
}

// 修改批量删除功能，支持删除多条数据
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的栏目')
    return
  }

  try {
    await ElMessageBox.alert(
      `是否确认删除以上栏目与及时性规则信息？`,
      '提示',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 区分已保存行和未保存的新行
    const savedRows = selectedRows.value.filter(row => row.id)
    const unsavedRows = selectedRows.value.filter(row => !row.id || row._isNew)

    // 显示加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在删除...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      let successCount = 0

      // 处理已保存的行（调用API删除）
      if (savedRows.length > 0) {
        // 收集所有选中行的ID
        const ids = savedRows.map(row => row.id as string)

        // 调用API一次性删除多行
        const res: any = await deleteColumnData({ ids })

        if (res?.code === 200 && res.res_code === '000000') {
          successCount += ids.length
          console.log(`成功删除 ${ids.length} 条已保存的栏目数据`)
        } else {
          ElMessage.error(res?.message || '删除已保存数据失败，请重试')
        }
      }

      // 处理未保存的新行（本地删除）
      if (unsavedRows.length > 0) {
        // 从表格数据中移除未保存的行
        unsavedRows.forEach(row => {
          // 从表格数据中移除
          const tableIndex = tableData.value.findIndex(item => item === row)
          if (tableIndex !== -1) {
            tableData.value.splice(tableIndex, 1)
          }

          // 从待保存列表中移除
          const pendingIndex = pendingNewRows.value.findIndex(item => item === row)
          if (pendingIndex !== -1) {
            pendingNewRows.value.splice(pendingIndex, 1)
          }
        })

        successCount += unsavedRows.length
        console.log(`成功删除 ${unsavedRows.length} 条未保存的新行`)
      }

      // 显示结果消息
      if (successCount > 0) {
        ElMessage.success(`成功删除 ${successCount} 条栏目数据`)
      }

      // 如果有已保存的行被删除，重新加载数据
      if (savedRows.length > 0) {
        await getWebsiteColumnData(route.query.websiteId as string)
      }

      // 清空选中行
      selectedRows.value = []

    } finally {
      // 关闭加载提示
      loadingInstance.close()
    }
  } catch {
    // 用户取消删除
    ElMessage.info('已取消删除')
  }
}

/** 下载二进制流文件
 * @param binFile 二进制文件流
 * @param fileName 文件名，例如：测试文本.txt
 * @param blobType Blob 对象的 type 属性给出文件的 MIME 类型，默认：'application/octet-stream'(用于通用二进制数据)
 */
const downloadBinaryFile = (binFile: string, fileName: string, blobType = 'application/octet-stream') => {
  // 去除文件名中可能存在的引号
  const cleanFileName = fileName.replace(/^["']|["']$/g, '') || fileName;
  console.log('处理后的文件名:', cleanFileName);

  // 处理二进制数据并创建 Blob 对象
  const blobObj = new Blob([binFile], { type: blobType })
  // 创建一个链接并设置下载属性
  const downloadLink: any = document.createElement('a')
  let url: any = window.URL // 兼容不同浏览器的 URL 对象
  url = url.createObjectURL(blobObj)
  downloadLink.href = url
  downloadLink.download = cleanFileName // 使用清理后的文件名
  // 将链接添加到 DOM 中，模拟点击
  document.body.appendChild(downloadLink)
  downloadLink.click()
  // 移除创建的链接和释放 URL 对象
  document.body.removeChild(downloadLink)
  window.URL.revokeObjectURL(url)
}

// 自定义激活编辑器条件方法
const activeCellMethod = ({ row }: { row: tableDataItem }) => {
  // 只有正在编辑中的新行才允许激活编辑
  return row && row._isNewEditing === true
}

// 禁止双击编辑
const handleCellDblclick = ({ row }: { row: tableDataItem }) => {
  if (!row._isNewEditing) {
    // 如果不是正在编辑的新行，则打开编辑弹窗
    if (row) {
      selectedRows.value = [row]
      const $table = tableRef.value
      if ($table) {
        $table.clearCheckboxRow()
        $table.setCheckboxRow(row, true)
      }
      openEditDialog()
    }
    return false
  }
  return true
}

// 处理页码变化
const handlePageChange = async (val: number) => {
  currentPage.value = val
  await getWebsiteColumnData(route.query.websiteId as string)
}

// 处理每页数量变化
const handleSizeChange = async (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  await getWebsiteColumnData(route.query.websiteId as string)
}
</script>

<template>
  <PageMain>
    <div class="flex justify-between">
      <div class="font-600">
        {{ `${route.query.networkName}网站及时性规则列表如下:` }}
      </div>
      <div class="flex items-center">
        <!-- 按钮从左到右: 导入、导出、新增、保存、编辑、返回、删除 -->
        <el-button color="#00706B" @click="openImportDialog" v-auth="['websitemanage.timeliness.import']">导入</el-button>
        <el-button color="#00706B" @click="handleDownload" v-auth="['websitemanage.timeliness.export']">导出</el-button>
        <el-button color="#00706B"
          @click="() => handleAddNewLine()" v-auth="['websitemanage.timeliness.add']">新增</el-button>
        <el-button color="#00706B" :disabled="pendingNewRows.length === 0"
          @click="handleSave" v-auth="['websitemanage.timeliness.save']">保存{{ pendingNewRows.length > 0 ? ` (${pendingNewRows.length})` : '' }}</el-button>
        <el-button color="#00706B" :disabled="selectedRows.length !== 1"
          @click="openEditDialog" v-auth="['websitemanage.timeliness.edit']">编辑</el-button>
        <el-button color="#00706B" @click="handleCancelEdit">返回</el-button>
        <el-button color="#00706B" @click="handleBatchDelete" :disabled="selectedRows.length === 0" :title="`已选中${selectedRows.length}行`" v-auth="['websitemanage.timeliness.delete']">
          删除
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="my-4">
      <vxe-table ref="tableRef" :data="tableData" border show-overflow
        :edit-config="{
          trigger: 'click',
          mode: 'row',
          activeMethod: activeCellMethod
        }"
        size="small" class="table-height-query"
        @edit-actived="handleEditActived"
        @edit-closed="handleEditClosed"
        @checkbox-change="handleCheckboxChange"
        @checkbox-all="handleCheckboxAll"
        @cell-dblclick="handleCellDblclick">
        <!-- 添加复选框列 -->
        <vxe-column type="checkbox" width="50" align="center"></vxe-column>
        <vxe-column align="center" field="column_type" title="栏目类型"
          :edit-render="{ name: 'input' }"></vxe-column>
        <vxe-column align="center" field="first_column" title="一级栏目"
          :edit-render="{ name: 'input' }"></vxe-column>
        <vxe-column align="center" field="second_column" title="二级栏目"
          :edit-render="{ name: 'input' }"></vxe-column>
        <vxe-column align="center" field="third_column" title="三级栏目"
          :edit-render="{ name: 'input' }"></vxe-column>
        <vxe-column align="center" field="ip_address" title="域名/IP"
          :edit-render="{ name: 'input' }"></vxe-column>
        <vxe-column align="center" field="timeliness_rule_type"
          title="及时性要求（周期）" :edit-render="{}">
          <template #edit="{ row }">
            <vxe-select v-model="row.timeliness_rule_type"
              :options="timelineNameGropus"></vxe-select>
          </template>
          <template #default="{ row }">
            {{ `每${timelineName(row.timeliness_rule_type)}` }}
          </template>
        </vxe-column>
        <vxe-column align="center" field="timeliness_rule_count"
          title="及时性要求（次数）" :edit-render="{}">
          <template #edit="{ row }">
            <vxe-number-input v-model="row.timeliness_rule_count"
              type="integer" min="1"></vxe-number-input>
          </template>
          <template #default="{ row }">
            {{ `${row.timeliness_rule_count}次` }}
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog v-model="dialogVisible" width="650" center align-center destroy-on-close>
      <el-form :model="form" ref="formRef" :rules="rules" label-position="right" label-width="120px" class="w-xl">
        <el-form-item label="栏目类型" prop="column_type" required>
          <el-input v-model="form.column_type" placeholder="请输入栏目类型"></el-input>
        </el-form-item>
        <el-form-item label="一级栏目" prop="first_column" required>
          <el-input v-model="form.first_column" placeholder="请输入一级栏目"></el-input>
        </el-form-item>
        <el-form-item label="二级栏目" prop="second_column">
          <el-input v-model="form.second_column" placeholder="请输入二级栏目"></el-input>
        </el-form-item>
        <el-form-item label="三级栏目" prop="third_column">
          <el-input v-model="form.third_column" placeholder="请输入三级栏目"></el-input>
        </el-form-item>
        <el-form-item label="域名/IP" prop="ip_address" required>
          <el-input v-model="form.ip_address" placeholder="请输入域名/IP"></el-input>
        </el-form-item>
        <el-form-item label="及时性要求" required>
          <div class="w-md flex justify-between items-center">
            <div class="mr-6">每</div>
            <el-select class="mr-3" v-model="form.timeliness_rule_type" placeholder="请选择周期">
              <el-option
                v-for="item in timelineNameGropus"
                :key="item.value"
                :label="timelineName(item.value)"
                :value="item.value">
              </el-option>
            </el-select>
            <el-input v-model="form.timeliness_rule_count" type="number"></el-input>
            <div class="ml-6">次</div>
          </div>
        </el-form-item>
      </el-form>
      <template #header="{ titleId }">
        <div :id="titleId" class="flex font-600 mb-6">编辑栏目及时性</div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="handleSubmit">保存</el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入弹窗 -->
    <el-dialog v-model="importDialogVisible" width="500" center align-center destroy-on-close>
      <div class="mb-4 flex flex-col items-center">
        <div class="flex items-center mb-4">
          <span class="mr-2">导入模板:</span>
          <el-button type="text" @click="downloadTemplate">点击下载及时性规则编辑模板</el-button>
        </div>

        <div class="flex items-center">
          <span class="mr-2">导入文件:</span>
          <div class="max-w-[360px]">
            <!-- 添加提示条件性显示 -->
            <div v-if="fileList.length > 0" class="mb-2 text-orange-500 text-xs">
              如需更换文件，请先点击文件右侧的"×"删除当前文件
            </div>
            <el-upload
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :limit="1"
              :file-list="fileList"
              :on-change="handleFileChange"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              accept=".xlsx,.xls"
            >
              <!-- 只有当fileList为空时才允许上传 -->
              <el-button color="#00706B" :disabled="fileList.length > 0">文件上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传xlsx/xls文件
                </div>
              </template>
            </el-upload>
          </div>
        </div>
      </div>
      <template #header="{ titleId }">
        <div :id="titleId" class="flex font-600 mb-6">导入</div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="confirmImport">确认</el-button>
          <el-button @click="cancelImport">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加在表格下方 -->
    <div class="flex justify-end mt-4">
      <el-pagination
        background
        layout="sizes, total, prev, pager, next"
        :page-sizes="[20, 30, 40, 50, 100]"
        :total="total"
        :page-count="pageCount"
        :page-size="pageSize"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      ></el-pagination>
    </div>
  </PageMain>
</template>

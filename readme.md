# 项目介绍

- 国家电网门户网站板块内容合规性检查监测管理后台
- 旨在管理对国网门户网站和国网各省市分公司门户网站内容的及时性、周期检查和检查规则设置以及最终的检查报告输出
- 此项目只是前端管理后台，对网站内容的检查周期、检查规则和检查报告输出，由管理后台进行编辑发送给实际执行检查工作的爬虫进行运行

------------

# 开发描述和注意事项

1. 本项目使用Vue3+Vite+Element-Plus+TypeScript为基础进行开发，且依赖于pnpm和node 18.18.0版本
2. 样式编写建议使用的是uno-css原子化css框架，避免组件文件大量style标签内容，除非遇到一些需要style标签才能方便解决的情况再考虑使用style标签内使用scss
3. Vue3的composition-Api均已经通过插件进行了全局引入，组件文件内可以直接使用，不需要import手动引入，element-plus组件同理，除了个别比如elmessage组件的指令调用用法需要引入，如发现composition-Api或者组件直接引用不起作用，请查看/src/types文件夹下文件查看
4. 针对开发和打包上线，请在.env.development和.env.production分别修改对应环境的请求地址字段进行开发或部署
5. 本后台管理前端采用了开源框架Fantistic-admin，文档详细，关于路由配置、模板生成、自定义封装组件的使用，建议详细参考文档：
[Fantistic-admin官方文档](https://fantastic-admin.hurui.me/ "Fantistic-admin官方文档")

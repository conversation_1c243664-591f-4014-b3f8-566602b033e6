/* stylelint-disable rule-empty-line-before */
<route lang="yaml">
  meta:
    title: 历史报告查询
  </route>
<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { renderAsync } from 'docx-preview'
import histortReportQueryApi from '@/api/modules/histortReportQuery'
import useBusinessStore from '@/store/modules/business'
import { extractFilenameFromResponse } from '@/utils'
import { saveFileWithPicker } from '@/utils'
// 数据
const tableData = ref([])
const { queryHistoryReports, downloadHistoryReport, patchDownloadHistoryReport } = histortReportQueryApi
const businessStore = useBusinessStore()

const unit_name_list = ref<any[]>([])

// 工具项
const unit_name = ref('')
// 检查时间范围
const dateRange = ref<[string, string]>(['', ''])

// 网页数据总条目数
const total = ref(0)
// 当前页码
const currentPage = ref(1)
// 总页码数量
const pageCount = ref(0)
// 每页显示的数据条目数
const pageSize = ref(20)

// 选中的行
const multipleSelection = ref([])

// 报告预览相关
const dialogVisible = ref(false)
const reportId = ref('')
const filePreview = ref<any>(null)
const isRenderReportSuccess = ref(false)

// 禁用未来月份的函数
const disableCurrentAndFutureDates = (date: Date) => {
  // 获取当前日期的年月
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() // 0-11

  // 获取所选日期的年月
  const selectedYear = date.getFullYear()
  const selectedMonth = date.getMonth() // 0-11

  // 如果所选年份大于当前年份，禁用
  if (selectedYear > currentYear) {
    return true
  }
  // 如果所选年份等于当前年份，但月份大于当前月份，禁用
  if (selectedYear === currentYear && selectedMonth > currentMonth) {
    return true
  }

  return false
}

onMounted(async () => {
  await businessStore.getNetworkUnitList()
  unit_name_list.value = businessStore.networkUnitOriginalList
  await fetchReportData()
})

const fetchReportData = async () => {
  // 处理 dateRange.value 可能为 null 的情况
  const dateRangeValue = dateRange.value || ['', '']
  const [start_date, end_date] = dateRangeValue

  const res = await queryHistoryReports({
    page_index: currentPage.value,
    page_size: pageSize.value,
    unit_name: unit_name.value,
    start_date,
    end_date
  })
  tableData.value = res?.data?.page_list || []
  total.value = res?.data?.total_num || 0
  pageCount.value = res?.data?.page_num || 0
}

// 事件处理
const handleQuery = async () => {
  // 重置到第一页
  currentPage.value = 1

  // 检查是否所有筛选条件都为空
  // 防止 dateRange.value 为 null 导致解构错误
  const dateRangeValue = dateRange.value || ['', '']
  const [start_date, end_date] = dateRangeValue
  const isAllFilterEmpty = !unit_name.value && (!start_date || start_date === '') && (!end_date || end_date === '')

  // 如果所有筛选条件都为空，则重新加载所有数据（与页面初次加载时一致）
  if (isAllFilterEmpty) {
    dateRange.value = ['', '']
    unit_name.value = ''
  }

  // 执行查询
  await fetchReportData()
}

const handlePageChange = async (val: number) => {
  currentPage.value = val
  await fetchReportData()
}

const handleSizeChange = async (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  await fetchReportData()
}

const handleSelectionChange = (selection: never[]) => {
  multipleSelection.value = selection
}
// 查看报告以弹窗形式打开，暂时不用代码保留
const handleViewReport = async (id: string) => {
  reportId.value = id
  dialogVisible.value = true
  try {
    const res: any = await downloadHistoryReport(id)
    const filename = extractFilenameFromResponse(res, '历史报告.docx')
    renderAsync(res.data, filePreview.value).then((resp) => {
      if (resp) {
        isRenderReportSuccess.value = true
      }
    }).catch(() => {
      dialogVisible.value = false
      ElMessage.warning('报告预览失败，请稍后再试')
    })
  } catch (error) {
    dialogVisible.value = false
    ElMessage.warning('获取报告失败，请稍后再试')
  }
}

const handleDownloadDoc = async () => {
  try {
    const res: any = await downloadHistoryReport(reportId.value)
    const filename = extractFilenameFromResponse(res, '历史报告.docx')
    downloadBinaryFile(res.data, filename)
  } catch (error) {
    ElMessage.warning('下载报告失败，请稍后再试')
  }
}

const handleBatchDownload = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }

  try {
    // 提取所有选中行的ID
    const ids = multipleSelection.value.map((item: any) => item.id)

    // 调用批量下载接口
    const res: any = await patchDownloadHistoryReport({ ids })

    // 确保响应是二进制数据
    if (res.data instanceof Blob) {
      // 下载返回的文件，使用适当的MIME类型
      const filename = extractFilenameFromResponse(res, '历史报告批量下载.zip')
      downloadBinaryFile(res.data, filename, 'application/vnd.rar')
      ElMessage.success('批量下载成功')
    } else {
      ElMessage.warning('下载格式错误，请稍后再试')
    }
  } catch (error) {
    console.error('批量下载错误:', error)
    ElMessage.warning('批量下载失败，请稍后再试')
  }
}

const handleCancel = () => {
  dialogVisible.value = false
}

// 新增：在新窗口查看报告（使用renderAsync渲染，带下载按钮）
const handleViewReportInNewWindow = async (id: string) => {
  try {
    const res: any = await downloadHistoryReport(id)
    const filename = extractFilenameFromResponse(res, '历史报告.docx')

    // 打开新窗口
    const newWindow = window.open('', '_blank')
    if (!newWindow) {
      ElMessage.warning('无法打开新窗口，请检查浏览器弹窗设置')
      return
    }

    // 写入基础HTML结构
    newWindow.document.write(`
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <title>${filename || '历史报告'}</title>
        <style>
          body { margin: 0; padding: 0; font-family: sans-serif; }
          .toolbar { padding: 12px; background: #f5f5f5; border-bottom: 1px solid #ddd; display: flex; justify-content: flex-end; }
          .preview-area { width: 100vw; height: calc(100vh - 56px); overflow: auto; background: #fafbfc; }
          .download-btn { background: #00706B; color: #fff; border: none; padding: 8px 18px; border-radius: 4px; font-size: 16px; cursor: pointer; }
          .download-btn:hover { background: #005f56; }
          .loading { display: flex; justify-content: center; align-items: center; height: calc(100vh - 56px); font-size: 16px; color: #666; }
          
          /* 复制原有的docx样式 */
          .docx-wrapper {
            padding: 20px;
          }
          .docx-wrapper section {
            width: auto !important;
          }
          .docx-wrapper table {
            width: 100% !important;
            table-layout: fixed !important;
            border: 1px solid #000;
          }
          .docx-wrapper tr {
            border: 1px solid #000;
          }
          .docx-wrapper td {
            border: 1px solid #000;
          }
        </style>
      </head>
      <body>
        <div class="toolbar">
          <button class="download-btn" id="customDownloadBtn">下载报告</button>
        </div>
        <div class="preview-area">
          <div class="loading" id="loadingText">正在加载报告预览...</div>
          <div id="docxContainer" style="display: none;"></div>
        </div>
        <script>
          let fileBlob = null;
          let fileName = ${JSON.stringify(filename)};
          
          // 下载按钮事件
          document.getElementById('customDownloadBtn').onclick = function() {
            if (fileBlob && window.opener && window.opener.saveFileWithPicker) {
              window.opener.saveFileWithPicker(fileBlob, fileName, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
            } else if (fileBlob) {
              // 回退下载方式
              const url = URL.createObjectURL(fileBlob);
              const a = document.createElement('a');
              a.href = url;
              a.download = fileName;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              URL.revokeObjectURL(url);
            } else {
              alert('文件数据未准备好，无法下载');
            }
          };
          
          // 监听主窗口传递的渲染内容
          window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'docx-rendered') {
              const container = document.getElementById('docxContainer');
              const loading = document.getElementById('loadingText');
              
              if (container && loading) {
                container.innerHTML = event.data.html;
                container.style.display = 'block';
                loading.style.display = 'none';
                fileBlob = event.data.blob;
              }
            }
            
            if (event.data && event.data.type === 'docx-error') {
              const loading = document.getElementById('loadingText');
              if (loading) {
                loading.textContent = '报告预览失败，请点击下载按钮获取文件';
                loading.style.color = '#f56c6c';
              }
            }
          });
        <\/script>
      </body>
      </html>
    `)
    newWindow.document.close()

    // 在主窗口中渲染文档，然后传递给新窗口
    // 创建临时容器进行渲染
    const tempContainer = document.createElement('div')
    tempContainer.style.display = 'none'
    document.body.appendChild(tempContainer)
    
    try {
      await renderAsync(res.data, tempContainer)
      
      // 渲染成功，传递HTML内容给新窗口
      newWindow.postMessage({
        type: 'docx-rendered',
        html: tempContainer.innerHTML,
        blob: res.data instanceof Blob ? res.data : new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })
      }, '*')
      
    } catch (error) {
      console.error('文档渲染失败:', error)
      newWindow.postMessage({ type: 'docx-error' }, '*')
    } finally {
      // 清理临时容器
      document.body.removeChild(tempContainer)
    }
    
  } catch (error) {
    ElMessage.warning('获取报告失败，请稍后再试')
    console.error('新窗口查看报告失败:', error)
  }
}

// 修改批量下载，支持路径选择
const handleBatchDownloadWithPicker = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }

  try {
    // 提取所有选中行的ID
    const ids = multipleSelection.value.map((item: any) => item.id)
    // 调用批量下载接口
    const res: any = await patchDownloadHistoryReport({ ids })
    
    // 确保响应是二进制数据
    if (res.data instanceof Blob) {
      const filename = extractFilenameFromResponse(res, '历史报告批量下载.zip')
      
      // 调用优化后的saveFileWithPicker
      const saveResult = await saveFileWithPicker(res.data, filename, 'application/zip')
      
      if (saveResult === true) {
        // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
      } else if (saveResult === 'default') {
        // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
      } else {
        // 用户取消操作
        ElMessage.info('下载已取消')
      }
    } else {
      ElMessage.warning('下载格式错误，请稍后再试')
    }
  } catch (error) {
    console.error('批量下载错误:', error)
    ElMessage.warning('批量下载失败，请稍后再试')
  }
}

/** 下载二进制流文件
 * @param binFile 二进制文件流
 * @param fileName 文件名，例如：测试文本.txt
 * @param blobType Blob 对象的 type 属性给出文件的 MIME 类型，默认：'application/octet-stream'(用于通用二进制数据)
 */
const downloadBinaryFile = (binFile: Blob | ArrayBuffer, fileName: string, blobType = 'application/octet-stream') => {
  // 去除文件名中可能存在的引号
  const cleanFileName = fileName.replace(/^["']|["']$/g, '') || fileName
  console.log('处理后的文件名:', cleanFileName)

  // 如果已经是Blob，直接使用，否则创建新的Blob
  const blobObj = binFile instanceof Blob ? binFile : new Blob([binFile], { type: blobType })
  // 创建一个链接并设置下载属性
  const downloadLink = document.createElement('a')
  let url: any = window.URL // 兼容不同浏览器的 URL 对象
  url = url.createObjectURL(blobObj)
  downloadLink.href = url
  downloadLink.download = cleanFileName // 设置下载的文件名
  // 将链接添加到 DOM 中，模拟点击
  document.body.appendChild(downloadLink)
  downloadLink.click()
  // 移除创建的链接和释放 URL 对象
  document.body.removeChild(downloadLink)
  window.URL.revokeObjectURL(url)
}

</script>

<template>
  <div>
    <PageMain>
      <div class="flex justify-between mb-4" v-auth="['historyreportquery.query']">
        <div class="w-auto flex items-center">
          <div class="flex items-center mr-6">
            <div class="mr-6 font-600">归属单位</div>
            <el-tree-select
              style="width: 200px;"
              v-model="unit_name"
              placeholder="请选择归属单位"
              filterable
              :props="{value: 'label'}"
              :data="unit_name_list"
              :render-after-expand="false"
              clearable
            >
            </el-tree-select>
          </div>

          <div class="flex items-center">
            <div class="mr-6 font-600">检查时间</div>
            <el-date-picker
              v-model="dateRange"
              type="monthrange"
              range-separator="-"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="YYYY-MM"
              :disabled-date="disableCurrentAndFutureDates" />
          </div>

          <div class="ml-6 flex items-center">
            <el-button color="#00706B" @click="handleQuery">查询</el-button>
          </div>
        </div>
      </div>

      <!-- 表格 -->
      <div class="my-4">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-bold mb-4">历史报告查询列表</h2>
          <el-button color="#00706B"
            @click="handleBatchDownloadWithPicker" v-auth="['historyreportquery.batchdownload']">批量下载</el-button>
        </div>
        <el-table
          :data="tableData"
          border
          size="small"
          class="table-height"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection"
            width="55"></el-table-column>
          <el-table-column align="center" type="index" label="序号"
            width="70"></el-table-column>
          <el-table-column align="center" prop="belonging_unit"
            label="归属单位" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="network_name"
            label="网站名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="network_type"
            label="网络类型"></el-table-column>
          <el-table-column align="center" prop="check_schedule"
            label="检查时间"></el-table-column>
          <el-table-column align="center" label="检查结果">
            <template #default="scope">
              <div class="flex items-center justify-center">
                <span class="mr-2"
                  v-if="scope.row.check_result">{{ scope.row.check_result }}</span>
                <el-button size="small" color="#00706B"
                  @click="handleViewReportInNewWindow(scope.row.id)" v-auth="['historyreportquery.viewreport']">查看报告</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 翻页组件 -->
      <div class="flex justify-end">
        <el-pagination background
          layout="sizes, total, prev, pager, next"
          :page-size="pageSize" :page-sizes="[20, 30, 40, 50, 100]"
          :total="total" :page-count="pageCount"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"></el-pagination>
      </div>

      <!-- Dialog 对话框 -->
      <el-dialog v-model="dialogVisible" fullscreen center
        align-center
        destroy-on-close @closed="isRenderReportSuccess = false">
        <div :class="isRenderReportSuccess ? '' : 'h-auto'"
          ref="filePreview"
          v-loading.fullscreen.lock="!isRenderReportSuccess"
          element-loading-text="正在加载报告预览"></div>
        <template v-if="isRenderReportSuccess" #title>
          <div class="flex font-600">报告预览</div>
        </template>
        <template v-if="isRenderReportSuccess" #footer>
          <el-button @click="handleCancel">取消</el-button>
          <el-button color="#00706B"
            @click="handleDownloadDoc">下载</el-button>
        </template>
      </el-dialog>
    </PageMain>
  </div>
</template>

<style lang="scss">
.docx-wrapper {
  section {
    width: auto !important;

    table {
      width: 100% !important;
      table-layout: fixed !important;
      border: 1px solid #000;
    }

    tr {
      border: 1px solid #000;
    }

    td {
      border: 1px solid #000;
    }
  }
}
</style>

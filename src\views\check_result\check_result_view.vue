/* stylelint-disable rule-empty-line-before */
<route lang="yaml">
  meta:
    title: 检查结果查看
  </route>
<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { renderAsync, renderDocument } from 'docx-preview'
import websiteMangementApi from '@/api/modules/websiteManagement'
import useBusinessStore from '@/store/modules/business'
// 数据
const tableData = ref([])

const { queryWebsiteReportRule, generateWebsiteReport, downloadWebsiteReport, updateWebsiteReportRule } = websiteMangementApi
const businessStore = useBusinessStore()

const unit_name_list = ref<any[]>([])

// 工具项
const unit_name = ref('')
const report_time = ref('')

// 网页数据总条目数
const total = ref(100)
// 当前页码
const currentPage = ref(1)
// 总页码数量
const pageCount = ref(0)
// 每页显示的数据条目数
const pageSize = ref(20)

const dialogVisible = ref(false)
const network_id = ref('')
const check_schedule = ref('')
const filePreview = ref<any>(null)
const isRenderReportSuccess = ref(false)

onMounted(async () => {
  await businessStore.getUnitNameList()
  unit_name_list.value = businessStore.unitNameOriginalList
  const res = await queryWebsiteReportRule({ page_index: currentPage.value, page_size: pageSize.value, unit_name: unit_name.value, report_time: report_time.value })
  tableData.value = res?.data?.reports
  total.value = res?.data?.total_num
  pageCount.value = res?.data?.page_num
})

// const getWebsiteReportData = async (page_index: number, page_size: number, unit_name?: string, report_time?: string) => {
//   const res = await queryWebsiteReportRule({ page_index, page_size, unit_name, report_time })
//   tableData.value = res?.data?.reports
//   total.value = res?.data?.total_num
//   pageCount.value = res?.data?.page_num
// }
// 事件处理
const handleQuery = async () => {
  const res = await queryWebsiteReportRule({ page_index: 1, page_size: 20, unit_name: unit_name.value, report_time: report_time.value })
  tableData.value = res?.data?.reports
  total.value = res?.data?.total_num
}

const handlePageChange = async (val: number) => {
  currentPage.value = val
  console.log(`当前页: ${val}`)
  const res = await queryWebsiteReportRule({ page_index: currentPage.value, page_size: pageSize.value, unit_name: unit_name.value, report_time: report_time.value })
  tableData.value = res?.data?.reports
  total.value = res?.data?.total_num
}

const handleSizeChange = async (val: number) => {
  pageSize.value = val
  console.log(`每页条数: ${val}`)
  const res = await queryWebsiteReportRule({ page_index: currentPage.value, page_size: pageSize.value, unit_name: unit_name.value, report_time: report_time.value })
  tableData.value = res?.data?.reports
  total.value = res?.data?.total_num
}

const handleChangeDate = async (date: string, id: string) => {
  console.log(`日期: ${date} ${id}`)
  await updateWebsiteReportRule({ check_schedule: date, network_id: id })
  await queryWebsiteReportRule({ page_index: currentPage.value, page_size: pageSize.value, unit_name: unit_name.value, report_time: report_time.value })
}

const handleMakeReport = async (id: string, schedule: string) => {
  network_id.value = id
  check_schedule.value = schedule
  dialogVisible.value = true
  const res: any = await generateWebsiteReport(id, schedule)
  console.log('生成报告', res)
  renderAsync(res, filePreview.value).then((resp) => {
    if (resp) {
      isRenderReportSuccess.value = true
    }
  }).catch((err) => {
    dialogVisible.value = false
    ElMessage.warning('生成报告失败，请检查网站规则、检查时间是否正确编辑')
  })
}

const handleDownloadDoc = async () => {
  const res: any = await downloadWebsiteReport(network_id.value, check_schedule.value)
  downloadBinaryFile(res, '检查报告.docx')
}

const handleCancel = () => {
  dialogVisible.value = false
}

/** 下载二进制流文件
 * @param binFile 二进制文件流
 * @param fileName 文件名，例如：测试文本.txt
 * @param blobType Blob 对象的 type 属性给出文件的 MIME 类型，默认：'application/octet-stream'(用于通用二进制数据)
 */
const downloadBinaryFile = (binFile: string, fileName: string, blobType = 'application/octet-stream') => {
  // 去除文件名中可能存在的引号
  const cleanFileName = fileName.replace(/^["']|["']$/g, '') || fileName;
  console.log('处理后的文件名:', cleanFileName);
  // 处理二进制数据并创建 Blob 对象
  const blobObj = new Blob([binFile], { type: blobType })
  // 创建一个链接并设置下载属性
  const downloadLink = document.createElement('a')
  let url: any = window.URL // 兼容不同浏览器的 URL 对象
  url = url.createObjectURL(blobObj)
  downloadLink.href = url
  downloadLink.download = cleanFileName // 设置下载的文件名
  // 将链接添加到 DOM 中，模拟点击
  document.body.appendChild(downloadLink)
  downloadLink.click()
  // 移除创建的链接和释放 URL 对象
  document.body.removeChild(downloadLink)
  window.URL.revokeObjectURL(url)
}

</script>

<template>
  <div>
    <PageMain>
      <div class="flex justify-between">
        <div class="w-auto flex justify-between">
          <div class="flex items-center mr-6">
            <div class="mr-6 font-600">归属单位</div>
            <el-tree-select
              style="width: 200px;"
              v-model="unit_name"
              placeholder="请选择归属单位"
              filterable
              :props="{value: 'label'}"
              :data="unit_name_list"
              :render-after-expand="false"
              clearable
            >
            </el-tree-select>
          </div>

          <div class="flex items-center">
            <div class="mr-6 font-600">检查时间</div>
            <el-date-picker
              clearable
              type="month"
              v-model="report_time"
              placeholder="请选择年月"
              value-format="YYYY-MM" />
          </div>

        </div>
        <div>
          <el-button type="primary"
            @click="handleQuery">查询</el-button>
        </div>
      </div>

      <!-- 表格 -->
      <div class="my-4">
        <el-table :data="tableData" border size="small" height="960"
          max-height="960">
          <el-table-column align="center" type="index" label="序号"
            fixed="left" width="50"></el-table-column>
          <el-table-column align="center" prop="id" label="ID"
            fixed="left" width="50"
            show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="belonging_unit"
            label="归属单位" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="network_name"
            label="网站名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="network_type"
            label="网络类型"></el-table-column>
          <el-table-column align="center" prop="check_schedule"
            label="检查时间">
            <template #default="scope">
              <div class="flex justify-center">
                <el-date-picker
                  clearable
                  type="month"
                  v-model="scope.row.check_schedule"
                  placeholder="请选择年月"
                  value-format="YYYY-MM"
                  @change="(date: string) => handleChangeDate(date, scope.row.id)" />
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="检查结果" fixed="right"
            width="200">
            <template #default="scope">
              <div class="flex justify-center flex-wrap">
                <el-button size="small" type="text"
                  @click="handleMakeReport(scope.row.id, scope.row.check_schedule)">生成报告</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 翻页组件 -->
      <div class="flex justify-end">
        <el-pagination background
          layout="sizes, total, prev, pager, next"
          :page-size="pageSize"
          :page-sizes="[20, 30, 40, 50, 100]" :total="total"
          :page-count="pageCount" @current-change="handlePageChange"
          @size-change="handleSizeChange"></el-pagination>
      </div>

      <!-- Dialog 对话框 -->
      <el-dialog v-model="dialogVisible" fullscreen center
        align-center
        destroy-on-close @closed="isRenderReportSuccess = false">
        <div :class="isRenderReportSuccess ? '' : 'h-auto'"
          ref="filePreview"
          v-loading.fullscreen.lock="!isRenderReportSuccess"
          element-loading-text="正在生成预览报告"></div>
        <template v-if="isRenderReportSuccess" #title>
          <div class="flex font-600">报告预览</div>
        </template>
        <template v-if="isRenderReportSuccess" #footer>
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary"
            @click="handleDownloadDoc">下载报告</el-button>
        </template>
      </el-dialog>
    </PageMain>
  </div>
</template>

<style lang="scss">
.docx-wrapper {
  section {
    width: auto !important;

    table {
      width: 100% !important;
      table-layout: fixed !important;
      border: 1px solid #000;
    }

    tr {
      border: 1px solid #000;
    }

    td {
      border: 1px solid #000;
    }
  }
}
</style>

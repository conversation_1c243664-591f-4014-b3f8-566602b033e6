import websiteManagementApi from '@/api/modules/websiteManagement'

const useBusinessStore = defineStore(
  // 唯一ID
  'business',
  () => {
    const unitNameList = ref<string[]>([])
    const unitNameOriginalList = ref<{ id: string, unit_name: string, organization: string }[]>([])

    // 网站管理、临时任务管理、历史报告查询页面的归属单位数据
    const networkUnitNameList = ref<string[]>([])
    // 网站管理、临时任务管理、历史报告查询页面的归属单位数据
    const networkUnitOriginalList = ref<{ id: string, unit_name: string, organization: string }[]>([])

    async function getUnitNameList() {
      const res = await websiteManagementApi.queryWebsiteUnit()
      unitNameList.value = res.data.map((item: any) => item.unit_name)
      unitNameOriginalList.value = res.data.filter((its: any)=>{
        return its.unit_list.length > 0
      })
      .map((it: any) => {
        return {
          label: it.organization,
          children: it.unit_list?.map((item: any) => {
            return {
              label: item.unit_name,
              value: item.id,
            }
          }) 
        }
      })
    }

    // 网站管理、临时任务管理、历史报告查询页面的归属单位数据
    async function getNetworkUnitList() {
      const res = await websiteManagementApi.queryNetworkUnit()
      networkUnitNameList.value = res.data.map((item: any) => item.unit_name)
      networkUnitOriginalList.value = res.data.filter((its: any)=>{
        return its.unit_list.length > 0
      }).map((it: any) => {
        return {
          label: it.organization,
          value: it.organization,
          children: it.unit_list?.map((item: any) => {
            return {
              label: item.unit_name,
              value: item.id,
            }
          }) 
        }
      })
    }

    return {
      unitNameList,
      unitNameOriginalList,
      networkUnitNameList,
      networkUnitOriginalList,
      getUnitNameList,
      getNetworkUnitList
    }
  },
)

export default useBusinessStore

import api from '../index'

export default {
  // 1. 导入网站excel
  importWebsiteExcel: (formData: FormData) => {
    return api.post('/network_info/excel', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 2. 导出网站excel
  exportWebsiteExcel: () => {
    return api.get('/network_info/excel', {
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/octet-stream'
      }
    })
  },

  // 3. 导入用户excel
  importUserExcel: (formData: FormData) => {
    return api.post('/user/excel', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 4. 导出用户excel
  exportUserExcel: () => {
    return api.get('/user/excel', {
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/octet-stream'
      }
    })
  },

  // 5. 下载网站编辑信息excel模板
  downloadWebsiteTemplate: () => {
    return api.get('/network_info/template', {
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/octet-stream'
      }
    })
  },

  // 6. 下载用户信息excel模板
  downloadUserTemplate: () => {
    return api.get('/user/template', {
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/octet-stream'
      }
    })
  },

  // 7. 下载外部链接excel模板
  downloadOffsiteTemplate: () => {
    return api.get('/offsite/template', {
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/octet-stream'
      }
    })
  },

  // 8. 下载网站栏目excel模板
  downloadColumnTemplate: () => {
    return api.get('/network_column/template', {
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/octet-stream'
      }
    })
  },

   // 9.角色模版下载
  downloadRoleTemplate: () => api.get('/role/template', { 
    responseType: 'blob',   headers: {
        'Content-Type': 'application/octet-stream'
      } 
  }),
  // 10.角色导入
  importRoleExcel: (data: FormData) => api.post('/role/excel', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),

  // 11.角色导出
  exportRoleExcel: () => api.get('/role/excel', { responseType: 'blob' ,   headers: {
        'Content-Type': 'application/octet-stream'
      }}),

   // 12.单位模版下载
   downloadUnitTemplate: () => api.get('/unit/template', { 
    responseType: 'blob',   headers: {
        'Content-Type': 'application/octet-stream'
      } 
  }),
  
  // 13.单位导入
  importUnitExcel: (data: FormData) => api.post('/unit/excel', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),

  // 14.单位导出
  exportUnitExcel: () => api.get('/unit/excel', { responseType: 'blob' ,   headers: {
        'Content-Type': 'application/octet-stream'
      }}),
}

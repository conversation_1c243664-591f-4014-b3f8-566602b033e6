import api from '../index'

export default {

  // 获取验证码
  captcha: () => api.get(`/captcha`),

  // 用户登录
  sgccLogin: (data: {
    name: string
    password: string
    captcha: string
    captcha_id: string
  }) => api.post('/login', data),

  // 对接国网巡检系统单点登录，改造于之前的登录接口
  sgccSSOLogin: (data: {
    token: string
    check: string
  }) => api.get(`/login?token=${data.token}&check=${data.check}`),

  // 刷新token
  refreshToken: () => api.get('/refresh'),

  // 登录
  login: (data: {
    account: string
    password: string
  }) => api.post('user/login', data, {
    baseURL: '/mock/',
  }),

  // 添加用户
  addUser: (data: {
    name: string,
    password?: string,
    role?: string | number,
    real_name?: string,
    unit_name?: string,
    belonging_unit_id?: string,
    phone?: string,
    email?: string,
    id_card?: string
  }) => api.post('/user', data),

  // 获取用户列表
  getUserList: (params: {
    page_index?: number,
    page_size?: number,
  }) => api.get('/user', {
    params: {
      ...params,
    },
  }),

  // 修改用户
  updateUser: (data: {
    id: string,
    name: string,
    role_id: string,
    real_name: string,
    belonging_unit_id: string,
    phone: string,
    email: string,
    // id_card: string
  }) => api.put('/user', data),

  // 删除用户
  deleteUser: (id: string | number) => api.delete(`/user?id=${id}`),

  // 获取权限
  permission: () => api.get('user/permission', {
    baseURL: '/mock/',
  }),

  // 修改密码
  passwordEdit: (data: {
    password: string
    newpassword: string
  }) => api.post('user/password/edit', data, {
    baseURL: '/mock/',
  }),

  // 获取偏好设置
  preferences: () => api.get('user/preferences', {
    baseURL: '/mock/',
  }),

  // 修改偏好设置
  preferencesEdit: (preferences: string) => api.post('user/preferences/edit', {
    preferences,
  }, {
    baseURL: '/mock/',
  }),

  // 获取标签栏固定标签页数据
  tabbar: () => api.get('user/tabbar', {
    baseURL: '/mock/',
  }),

  // 修改标签栏固定标签页数据
  tabbarEdit: (tabbar: string) => api.post('user/tabbar/edit', {
    tabbar,
  }, {
    baseURL: '/mock/',
  }),

  // 获取收藏夹
  favorites: () => api.get('user/favorites', {
    baseURL: '/mock/',
  }),

  // 修改收藏夹
  favoritesEdit: (favorites: string) => api.post('user/favorites/edit', {
    favorites,
  }, {
    baseURL: '/mock/',
  }),

  // 查看用户网站
  getUserNetworks: (userId: string, shareFlag: string = '0') =>
    api.get(`/userNetworkInfo/${userId}?share_flag=${shareFlag}`),

  // 给用户分配网站
  assignNetworksToUser: (userId: string, data: {
    network_info_ids: string[]
  }) => api.post(`/userNetworkInfo/${userId}`, data),

  // 取消分配给用户的网站
  removeNetworksFromUser: (userId: string, data: {
    network_info_ids: string[]
  }) => api.delete(`/userNetworkInfo/${userId}`, { data }),
}

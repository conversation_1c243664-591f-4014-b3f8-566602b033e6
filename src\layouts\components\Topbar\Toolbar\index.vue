<script setup lang="ts">
import LeftSide from './leftSide.vue'
import RightSide from './rightSide.vue'
import useSettingsStore from '@/store/modules/settings'

defineOptions({
  name: 'Toolbar',
})

const settingsStore = useSettingsStore()
</script>

<template>
  <div class="toolbar-container flex items-center justify-between">
    <div class="left-side h-full flex items-center of-hidden pe-16 ps-2">
      <LeftSide />
    </div>
    <div v-show="['side', 'single', 'only-side', 'side-panel'].includes(settingsStore.settings.menu.mode)" class="h-full flex items-center justify-end px-2">
      <RightSide />
    </div>
  </div>
</template>

<style scoped>
.toolbar-container {
  height: var(--g-toolbar-height);
  margin-left: 0;
  color: #fff;

  /* background-color: var(--g-container-bg); */
  background-color: #00706b;
  transition: background-color 0.3s;

  .left-side {
    mask-image: linear-gradient(to right, #000 0%, #000 calc(100% - 50px), transparent);

    [dir="rtl"] & {
      mask-image: linear-gradient(to left, #000 0%, #000 calc(100% - 50px), transparent);
    }
  }
}
</style>

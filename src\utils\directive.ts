import type { App } from 'vue'
import mediumZoom from 'medium-zoom'

// 扩展HTMLElement类型以支持自定义属性
interface ExtendedHTMLElement extends HTMLElement {
  __originalDisplay__?: string
  __authHidden__?: boolean
  __timeagoSetInterval__?: number
  __permissionUpdateHandler__?: () => void
}

export default function directive(app: App) {
  // 权限检查函数
  function checkPermission(el: ExtendedHTMLElement, binding: any, authFunction: (value: any) => boolean) {
    const hasAuth = authFunction(binding.value)

    // 如果元素已经被移除，需要重新创建
    if (!hasAuth && el.parentNode) {
      // 保存原始元素信息
      if (!el.__originalDisplay__) {
        el.__originalDisplay__ = el.style.display || ''
      }
      el.style.display = 'none'
      el.__authHidden__ = true
    } else if (hasAuth && el.__authHidden__) {
      // 恢复显示
      el.style.display = el.__originalDisplay__ || ''
      el.__authHidden__ = false
    }
  }

  // 注册 v-auth 指令
  app.directive('auth', {
    mounted: (el: ExtendedHTMLElement, binding) => {
      const { auth } = useAuth()
      checkPermission(el, binding, auth)

      // 监听权限更新事件
      const handlePermissionUpdate = () => {
        checkPermission(el, binding, auth)
      }
      document.addEventListener('permissions-updated', handlePermissionUpdate)
      el.__permissionUpdateHandler__ = handlePermissionUpdate
    },
    updated: (el: ExtendedHTMLElement, binding) => {
      const { auth } = useAuth()
      checkPermission(el, binding, auth)
    },
    beforeUnmount: (el: ExtendedHTMLElement) => {
      if (el.__permissionUpdateHandler__) {
        document.removeEventListener('permissions-updated', el.__permissionUpdateHandler__)
        delete el.__permissionUpdateHandler__
      }
    },
  })

  // 注册 v-auth-all 指令
  app.directive('auth-all', {
    mounted: (el: ExtendedHTMLElement, binding) => {
      const { authAll } = useAuth()
      checkPermission(el, binding, authAll)

      // 监听权限更新事件
      const handlePermissionUpdate = () => {
        checkPermission(el, binding, authAll)
      }
      document.addEventListener('permissions-updated', handlePermissionUpdate)
      el.__permissionUpdateHandler__ = handlePermissionUpdate
    },
    updated: (el: ExtendedHTMLElement, binding) => {
      const { authAll } = useAuth()
      checkPermission(el, binding, authAll)
    },
    beforeUnmount: (el: ExtendedHTMLElement) => {
      if (el.__permissionUpdateHandler__) {
        document.removeEventListener('permissions-updated', el.__permissionUpdateHandler__)
        delete el.__permissionUpdateHandler__
      }
    },
  })
  app.directive('timeago', {
    mounted: (el: ExtendedHTMLElement, binding) => {
      el.textContent = useTimeago().format(binding.value)
      if (binding.modifiers.interval) {
        el.__timeagoSetInterval__ = setInterval(() => {
          el.textContent = useTimeago().format(binding.value)
        }, 1000)
      }
    },
    beforeUnmount: (el: ExtendedHTMLElement, binding) => {
      if (binding.modifiers.interval) {
        clearInterval(el.__timeagoSetInterval__)
      }
    },
  })
  // 注册 medium-zoom 指令
  app.directive('zoomable', {
    mounted: (el) => {
      mediumZoom(el, {
        background: 'var(--g-bg)',
      })
    },
  })
}

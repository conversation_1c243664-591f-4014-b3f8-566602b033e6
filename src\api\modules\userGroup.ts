import api from '../index'

export default {
  // 查询用户组
  getUserGroups: () => api.get('/userGroup'),

  // 新增用户组
  addUserGroup: (data: {
    unit_id: string
    datalist: Array<{
      name: string
    }>
  }) => api.post('/userGroup', data),

  // 编辑用户组名称
  updateUserGroup: (data: {
    id: string
    name: string
  }) => api.put('/userGroup', data),

  // 删除用户组
  deleteUserGroup: (data: {
    ids: string[]
  }) => api.delete('/userGroup', { data }),

  // 查看用户组网站
  getUserGroupNetworks: (userGroupId: string, shareFlag: string = '1') =>
    api.get(`/userGroupNetworkInfo/${userGroupId}?share_flag=${shareFlag}`),

  // 给用户组增加网站
  addNetworksToUserGroup: (userGroupId: string, data: {
    network_info_ids: string[]
  }) => api.post(`/userGroupNetworkInfo/${userGroupId}`, data),

  // 给用户组移除网站
  removeNetworksFromUserGroup: (userGroupId: string, data: {
    network_info_ids: string[]
  }) => api.delete(`/userGroupNetworkInfo/${userGroupId}`, { data }),

  // 查看用户组的用户
  getUserGroupUsers: (userGroupId: string, params?: {
    page_index?: number
    page_size?: number
    selected_flag?: string | boolean
  }) => api.get(`/userGroupUserInfo/${userGroupId}`, { params }),

  // 给用户组增加/移除用户
  updateUserGroupUsers: (userGroupId: string, data: {
    add_user_ids: string[]
    del_user_ids: string[]
  }) => api.put(`/userGroupUserInfo/${userGroupId}`, data),
} 
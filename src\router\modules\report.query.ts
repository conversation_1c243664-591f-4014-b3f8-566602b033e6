import type { RouteRecordRaw } from 'vue-router'
import { $t } from '@/locales'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/reportquery',
  component: Layout,
  redirect: '/reportquery/historyreportquery',
  name: 'reportQuery',
  meta: {
    title: $t('route.reportquery.historyreportquery'),
    icon: 'ep:document',
    breadcrumb: false,
    defaultOpened : true,
    activeMenu: '/reportquery/historyreportquery',
    auth: ['historyreportquery']
  },
  children: [
    {
      path: 'historyreportquery',
      name: 'historyReportQuery',
      component: () => import('@/views/history_report/history_report_query.vue' ),
      meta: {
        title: $t('route.reportquery.historyreportquery'),
        menu: false,
        activeMenu: '/reportquery',
      },
    },
  ],
}

export default routes

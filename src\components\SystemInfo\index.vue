<i18n>
{
  "zh-cn": {
    "title": "系统信息",
    "lastBuildTime": "最后编译时间",
    "deps": "生产环境依赖",
    "devDeps": "开发环境依赖"
  },
  "zh-tw": {
    "title": "系統信息",
    "lastBuildTime": "最後編譯時間",
    "deps": "生產環境依賴",
    "devDeps": "開發環境依賴"
  },
  "en": {
    "title": "System Info",
    "lastBuildTime": "Last Build Time",
    "deps": "Production Dependencies",
    "devDeps": "Development Dependencies"
  }
}
</i18n>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import eventBus from '@/utils/eventBus'

const { t } = useI18n()

const isShow = ref(false)

const { pkg, lastBuildTime } = __SYSTEM_INFO__

onMounted(() => {
  eventBus.on('global-system-info-toggle', () => {
    isShow.value = !isShow.value
  })
})
</script>

<template>
  <HSlideover v-model="isShow" :title="t('title')">
    <div class="px-4">
      <h2 class="m-0 text-lg font-bold">
        {{ t('lastBuildTime') }}
      </h2>
      <div class="my-4 text-center text-lg font-sans">
        {{ lastBuildTime }}
      </div>
    </div>
    <div class="px-4">
      <h2 class="m-0 text-lg font-bold">
        {{ t('deps') }}
      </h2>
      <ul class="list-none pl-0 text-sm">
        <li v-for="(val, key) in (pkg.dependencies as object)" :key="key" class="flex items-center justify-between rounded px-2 py-1.5 hover-bg-stone-1 dark-hover-bg-stone-9">
          <div class="font-bold">
            {{ key }}
          </div>
          <div class="font-sans">
            {{ val }}
          </div>
        </li>
      </ul>
    </div>
    <div class="px-4">
      <h2 class="m-0 text-lg font-bold">
        {{ t('devDeps') }}
      </h2>
      <ul class="list-none pl-0 text-sm">
        <li v-for="(val, key) in (pkg.devDependencies as object)" :key="key" class="flex items-center justify-between rounded px-2 py-1.5 hover-bg-stone-1 dark-hover-bg-stone-9">
          <div class="font-bold">
            {{ key }}
          </div>
          <div class="font-sans">
            {{ val }}
          </div>
        </li>
      </ul>
    </div>
  </HSlideover>
</template>

<route lang="yaml">
  meta:
    title: 临时任务管理
  </route>
  
  <script setup lang="ts">
  import { ElMessage, ElMessageBox } from 'element-plus'
  import temporaryTaskApi from '@/api/modules/temporaryTask'
  import websiteManageApi from '@/api/modules/websiteManagement'
  import useBusinessStore from '@/store/modules/business'
  import { renderAsync } from 'docx-preview'
  import { extractFilenameFromResponse, saveFileWithPicker } from '@/utils'
  const router = useRouter()
  const { queryInspectionTasks, downloadInspectionReport, queryNetworkNames, queryNetworkDetail, createInspectionTask, queryInspectionColumns, deleteInspectionTask } = temporaryTaskApi
  const { queryColumnData } = websiteManageApi
  const businessStore = useBusinessStore()
  
  // 数据
  const tableData = ref([])
  
  // 工具项
  const unit_name = ref('')
  const network_name = ref('')
  const start_time = ref('')
  const task_status = ref('')
  
  const unit_name_list = ref<any[]>([])
  
  // 任务状态选项
  const statusOptions = [
    { label: '巡检中', value: '巡检中' },
    { label: '已完成', value: '已完成' },
    { label: '任务异常', value: '任务异常' }
  ]
  
  // 网络类型选项
  const networkTypeOptions = [
    { label: '内网', value: '内网' },
    { label: '外网', value: '外网' }
  ]
  
  // 网页数据总条目数
  const total = ref(0)
  // 当前页码
  const currentPage = ref(1)
  // 总页码数量
  const pageCount = ref(0)
  // 每页显示的数据条目数
  const pageSize = ref(20)
  
  // 报告预览相关
  const reportDialogVisible = ref(false)
  const filePreview = ref<any>(null)
  const isRenderReportSuccess = ref(false)
  const currentTaskId = ref('')
  
  // 新建任务相关
  const createTaskDialogVisible = ref(false)
  const selectedUnitName = ref('')
  const selectedNetworkName = ref('')
  const selectedNetworkType = ref('')
  const networkIp = ref('')
  const websiteId = ref('')
  const networkNameList = ref<string[]>([])
const columnTableData = ref<any[]>([])
  const checkLevel = ref('')
  const dateRangeDialogVisible = ref(false)
  const dateRange = ref<any>('')
  const multipleSelection = ref<string[]>([])
  const isLoadingColumns = ref(false)
  const columnTableTotal = ref(20)
  const columnTableCurrentPage = ref(1)
  const columnTablePageCount = ref(0)
  const columnTablePageSize = ref(20)
  
  
  
  // 栏目详情相关
  const columnDialogVisible = ref(false)
  const columnDetailData = ref<any[]>([])
  const currentTaskIdForColumns = ref('')
  const columnTotal = ref(0)
  const columnCurrentPage = ref(1)
  const columnPageCount = ref(0)
  const columnPageSize = ref(20)
  const loadingColumnDetails = ref(false)
  
  // 为日期范围选择器添加专用的禁用函数
  const disableFutureDate = (date: Date) => {
    // 获取当前日期（不含时间部分）
    const today = new Date()
    today.setHours(0, 0, 0, 0)
  
    // 对比日期（不带时间）
    return date > today
  }
  
  onMounted(async () => {
    await loadInitialData()
  })
  
  const loadInitialData = async () => {
    try {
      // 加载归属单位列表
      await businessStore.getNetworkUnitList()
      unit_name_list.value = businessStore.networkUnitOriginalList
      // 加载任务列表
      await loadTaskList()
    } catch (error) {
      console.error('初始化数据加载失败:', error)
      ElMessage.error('数据加载失败，请刷新页面重试')
    }
  }
  
  const loadTaskList = async () => {
    try {
      const res: any = await queryInspectionTasks({
        page_index: currentPage.value,
        page_size: pageSize.value,
        unit_name: unit_name.value,
        start_time: start_time.value,
        network_name: network_name.value,
        status: task_status.value
      })
  
      if (res?.code === 200 && res.res_code === '000000') {
        tableData.value = res?.data?.page_list || []
        total.value = res?.data?.total_num || 0
        pageCount.value = res?.data?.page_num || 0
      } else {
        ElMessage.warning(res.message || res.data || '任务列表加载失败')
      }
    } catch (error) {
      console.error('加载任务列表失败:', error)
      ElMessage.error('任务列表加载失败')
    }
  }
  
  // 事件处理
  const handleQuery = async () => {
    currentPage.value = 1
    await loadTaskList()
  }
  
  // 监听归属单位变化，获取对应的网站名称列表
  watch(selectedUnitName, async (newValue, oldValue) => {
    // 当归属单位发生变化时（不管是变为新值还是清空），都清空相关字段
    if (oldValue !== newValue) {
      selectedNetworkName.value = ''
      selectedNetworkType.value = ''
      networkIp.value = ''
      websiteId.value = ''
    }
  
    if (newValue) {
      try {
        const res: any = await queryNetworkNames(newValue)
        if (res?.code === 200 && res.res_code === '000000') {
          networkNameList.value = res?.data || []
        } else {
          networkNameList.value = []
          ElMessage.error('获取网站名称列表失败')
        }
      } catch (error) {
        console.error('获取网站名称列表失败:', error)
        networkNameList.value = []
        ElMessage.error('获取网站名称列表失败')
      }
    } else {
      // 当归属单位被清空时，清空网站名称列表
      networkNameList.value = []
    }
  })
  
  // 修改handleAdd方法，不再直接加载网站名称列表
  const handleAdd = async () => {
    // 重置新建任务相关状态
    handleResetCreateTask()
  
    // 显示新建任务弹窗
    createTaskDialogVisible.value = true
  }
  
  // 预留检查栏目点击方法
  const handleViewColumns = async (row: any) => {
    columnDialogVisible.value = true
    currentTaskIdForColumns.value = row.id
    columnCurrentPage.value = 1
    await loadColumnDetails()
  }
  
  const handleViewReport = async (taskId: string) => {
    reportDialogVisible.value = true
    isRenderReportSuccess.value = false
    currentTaskId.value = taskId
  
    try {
      const res: any = await downloadInspectionReport(taskId)
      const filename = extractFilenameFromResponse(res, '巡检报告.docx')
      renderAsync(res.data, filePreview.value).then((resp) => {
        if (resp) {
          isRenderReportSuccess.value = true
        }
      }).catch(() => {
        reportDialogVisible.value = false
        ElMessage.warning('报告预览失败，请稍后再试')
      })
    } catch (error) {
      reportDialogVisible.value = false
      ElMessage.warning('获取报告失败，请稍后再试')
    }
  }
  
  const handleDownloadDoc = async () => {
    try {
      const res = await downloadInspectionReport(currentTaskId.value)
      const filename = extractFilenameFromResponse(res, '巡检报告.docx')
      console.log('下载名字', filename)
      downloadBinaryFile(res.data, filename)
    } catch (error) {
      ElMessage.warning('下载报告失败，请稍后再试')
    }
  }
  
  // 在新窗口查看报告（
  const handleViewReportInNewWindow = async (taskId: string) => {
    try {
      const res: any = await downloadInspectionReport(taskId)
      const filename = extractFilenameFromResponse(res, '巡检报告.docx')

      // 打开新窗口
      const newWindow = window.open('', '_blank')
      if (!newWindow) {
        ElMessage.warning('无法打开新窗口，请检查浏览器弹窗设置')
        return
      }

      // 写入基础HTML结构
      newWindow.document.write(`
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
          <meta charset="UTF-8">
          <title>${filename || '巡检报告'}</title>
          <style>
            body { margin: 0; padding: 0; font-family: sans-serif; }
            .toolbar { padding: 12px; background: #f5f5f5; border-bottom: 1px solid #ddd; display: flex; justify-content: flex-end; }
            .preview-area { width: 100vw; height: calc(100vh - 56px); overflow: auto; background: #fafbfc; }
            .download-btn { background: #00706B; color: #fff; border: none; padding: 8px 18px; border-radius: 4px; font-size: 16px; cursor: pointer; }
            .download-btn:hover { background: #005f56; }
            .loading { display: flex; justify-content: center; align-items: center; height: calc(100vh - 56px); font-size: 16px; color: #666; }
            
            /* 复制原有的docx样式 */
            .docx-wrapper {
              padding: 20px;
            }
            .docx-wrapper section {
              width: auto !important;
            }
            .docx-wrapper table {
              width: 100% !important;
              table-layout: fixed !important;
              border: 1px solid #000;
            }
            .docx-wrapper tr {
              border: 1px solid #000;
            }
            .docx-wrapper td {
              border: 1px solid #000;
            }
          </style>
        </head>
        <body>
          <div class="toolbar">
            <button class="download-btn" id="customDownloadBtn">下载报告</button>
          </div>
          <div class="preview-area">
            <div class="loading" id="loadingText">正在加载报告预览...</div>
            <div id="docxContainer" style="display: none;"></div>
          </div>
          <script>
            let fileBlob = null;
            let fileName = ${JSON.stringify(filename)};
            
            // 下载按钮事件
            document.getElementById('customDownloadBtn').onclick = function() {
              if (fileBlob && window.opener && window.opener.saveFileWithPicker) {
                window.opener.saveFileWithPicker(fileBlob, fileName, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
              } else if (fileBlob) {
                // 回退下载方式
                const url = URL.createObjectURL(fileBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
              } else {
                alert('文件数据未准备好，无法下载');
              }
            };
            
            // 监听主窗口传递的渲染内容
            window.addEventListener('message', function(event) {
              if (event.data && event.data.type === 'docx-rendered') {
                const container = document.getElementById('docxContainer');
                const loading = document.getElementById('loadingText');
                
                if (container && loading) {
                  container.innerHTML = event.data.html;
                  container.style.display = 'block';
                  loading.style.display = 'none';
                  fileBlob = event.data.blob;
                }
              }
              
              if (event.data && event.data.type === 'docx-error') {
                const loading = document.getElementById('loadingText');
                if (loading) {
                  loading.textContent = '报告预览失败，请点击下载按钮获取文件';
                  loading.style.color = '#f56c6c';
                }
              }
            });
          <\/script>
        </body>
        </html>
      `)
      newWindow.document.close()

      // 在主窗口中渲染文档，然后传递给新窗口
      // 创建临时容器进行渲染
      const tempContainer = document.createElement('div')
      tempContainer.style.display = 'none'
      document.body.appendChild(tempContainer)
      
      try {
        await renderAsync(res.data, tempContainer)
        
        // 渲染成功，传递HTML内容给新窗口
        newWindow.postMessage({
          type: 'docx-rendered',
          html: tempContainer.innerHTML,
          blob: res.data instanceof Blob ? res.data : new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })
        }, '*')
        
      } catch (error) {
        console.error('文档渲染失败:', error)
        newWindow.postMessage({ type: 'docx-error' }, '*')
      } finally {
        // 清理临时容器
        document.body.removeChild(tempContainer)
      }
      
    } catch (error) {
      ElMessage.warning('获取报告失败，请稍后再试')
      console.error('新窗口查看报告失败:', error)
    }
  }
  
  // 修改downloadBinaryFile为支持路径选择的版本
  const handleDownloadDocWithPicker = async () => {
    try {
      const res: any = await downloadInspectionReport(currentTaskId.value)
      const filename = extractFilenameFromResponse(res, '巡检报告.docx')
      
      // 调用优化后的saveFileWithPicker
      const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
      
      if (saveResult === true) {
        // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
      } else if (saveResult === 'default') {
        // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
      } else {
        // 用户取消操作
        ElMessage.info('下载已取消')
      }
    } catch (error) {
      console.error('下载报告失败:', error)
      ElMessage.warning('下载报告失败，请稍后再试')
    }
  }
  
  const handleDelete = (index: number, row: any) => {
    ElMessageBox.confirm('确定要删除此任务吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        const res: any = await deleteInspectionTask({ ids: [row.id] })
        if (res?.code === 200 && res.res_code === '000000') {
          ElMessage.success(res.message || '删除成功')
          await loadTaskList()
        } else {
          ElMessage.warning(res?.message || res.data || '删除失败')
        }
      } catch (error) {
        console.error('删除任务失败:', error)
        ElMessage.error('删除失败，请稍后重试')
      }
    }).catch(() => {
      ElMessage.info('已取消删除')
    })
  }
  
  const handlePageChange = async (val: number) => {
    currentPage.value = val
    await loadTaskList()
  }
  
  const handleSizeChange = async (val: number) => {
    pageSize.value = val
    currentPage.value = 1
    await loadTaskList()
  }
  
  const handleReportCancel = () => {
    reportDialogVisible.value = false
  }
  
  /** 下载二进制流文件
   * @param binFile 二进制文件流
   * @param fileName 文件名，例如：测试文本.txt
   * @param blobType Blob 对象的 type 属性给出文件的 MIME 类型，默认：'application/octet-stream'(用于通用二进制数据)
   */
  const downloadBinaryFile = (binFile: any, fileName: string, blobType = 'application/octet-stream') => {
    // 去除文件名中可能存在的引号
    const cleanFileName = fileName.replace(/^["']|["']$/g, '') || fileName
    console.log('处理后的文件名:', cleanFileName)
  
    // 处理二进制数据并创建 Blob 对象
    const blobObj = new Blob([binFile], { type: blobType })
    // 创建一个链接并设置下载属性
    const downloadLink: any = document.createElement('a')
    let url: any = window.URL // 兼容不同浏览器的 URL 对象
    url = url.createObjectURL(blobObj)
    downloadLink.href = url
    downloadLink.download = cleanFileName // 使用清理后的文件名
    // 将链接添加到 DOM 中，模拟点击
    document.body.appendChild(downloadLink)
    downloadLink.click()
    // 移除创建的链接和释放 URL 对象
    document.body.removeChild(downloadLink)
    window.URL.revokeObjectURL(url)
  }
  
  // 新建任务相关方法
  const handleConfirmWebsiteSelection = async () => {
    if (!selectedUnitName.value) {
      ElMessage.warning('请选择归属单位')
      return
    }
    if (!selectedNetworkName.value) {
      ElMessage.warning('请选择网站名称')
      return
    }
    if (!selectedNetworkType.value) {
      ElMessage.warning('请选择网络类型')
      return
    }
  
    try {
      // 查询网站详情
      const res: any = await queryNetworkDetail({
        unit_name: selectedUnitName.value,
        network_name: selectedNetworkName.value,
        network_type: selectedNetworkType.value
      })
  
      if (res?.code === 200 && res?.res_code === '000000') {
        networkIp.value = res.data.network_ip || ''
        websiteId.value = res.data.id
        checkLevel.value = res.data.check_level || ''
        if (!res.data) {
          ElMessage.warning(res.message)
          return
        }
        // 获取网站栏目数据
        await loadWebsiteColumnData(res.data.id)
      } else {
        ElMessage.warning(res.message || '获取网站详情失败')
      }
    } catch (error) {
      console.error('获取网站详情失败:', error)
      ElMessage.error('获取网站详情失败')
    }
  }
  
  const loadWebsiteColumnData = async (id: string) => {
    if (!id) {
      ElMessage.warning('网站ID不能为空')
      return
    }
  
    isLoadingColumns.value = true
    try {
      const res: any = await queryColumnData({
        network_id: id,
        page_index: columnTableCurrentPage.value,
        page_size: columnTablePageSize.value
      })
      if (res?.code === 200 && res?.res_code === '000000') {
        columnTableData.value = res.data?.page_list || []
        columnTableTotal.value = res.data?.total_num || 0
        columnTablePageCount.value = res.data?.page_num || 0
      } else {
        columnTableData.value = []
        checkLevel.value = ''
        ElMessage.warning(res?.message || '未获取到栏目数据')
      }
    } catch (error) {
      ElMessage.error('获取栏目数据失败')
    } finally {
      isLoadingColumns.value = false
    }
  }
  
  const handleColumnSelectionChange = (val: any[]) => {
    multipleSelection.value = val.length ? val.map(item => item.id) : []
  }
  
  const timelineName = (ruleType: string) => {
    if (ruleType === 'DAYS') return '自然日'
    if (ruleType === 'WORK_DAYS') return '工作日'
    if (ruleType === 'YEARS') return '年'
    if (ruleType === 'WEEKS') return '周'
    if (ruleType === 'MONTHS') return '月'
    if (ruleType === 'QUARTERS') return '季度'
    return ruleType
  }
  
  const handleResetCreateTask = () => {
    // 重置所有选择的数据
    selectedUnitName.value = ''
    selectedNetworkName.value = ''
    selectedNetworkType.value = ''
    networkIp.value = ''
    websiteId.value = ''
    columnTableData.value = []
    checkLevel.value = ''
    multipleSelection.value = []
  }
  
  const handleExecute = () => {
    if (!websiteId.value) {
      ElMessage.warning('请先选择网站并获取栏目数据')
      return
    }
  
    if (!multipleSelection.value.length) {
      ElMessage.warning('请选择需要检查的栏目')
      return
    }
  
    // 显示日期选择弹窗
    dateRangeDialogVisible.value = true
  }
  
  const handleCancelDateRange = () => {
    dateRangeDialogVisible.value = false
    dateRange.value = ''
  }
  
  const handleConfirmDateRange = async () => {
    if (!dateRange.value || !Array.isArray(dateRange.value) || dateRange.value.length !== 2) {
      ElMessage.warning('请选择有效的日期范围')
      return
    }
  
    const [startDate, endDate] = dateRange.value
  
    try {
      const res: any = await createInspectionTask({
        website_id: websiteId.value,
        column_ids: multipleSelection.value,
        start_date: startDate,
        end_date: endDate
      })
  
      if (res?.code === 200 && res.res_code === '000000') {
        ElMessage.success(res.message || '任务创建成功')
        dateRangeDialogVisible.value = false
        createTaskDialogVisible.value = false
        await loadTaskList() // 刷新任务列表
      } else {
        ElMessage.warning(res?.message || res.data || '任务创建失败')
      }
    } catch (error) {
      console.error('创建任务失败:', error)
      ElMessage.error('任务创建失败')
    }
  }
  
  // 加载栏目详情数据
  const loadColumnDetails = async () => {
    if (!currentTaskIdForColumns.value) {
      ElMessage.warning('任务ID不能为空')
      return
    }
  
    loadingColumnDetails.value = true
    try {
      const res: any = await queryInspectionColumns(currentTaskIdForColumns.value, {
        page_index: columnCurrentPage.value,
        page_size: columnPageSize.value
      })
  
      if (res?.code === 200 && res.res_code === '000000') {
        columnDetailData.value = res?.data?.page_list || []
        columnTotal.value = res?.data?.total_num || 0
        columnPageCount.value = res?.data?.page_num || 0
      } else {
        ElMessage.warning(res?.message || '获取栏目详情失败')
      }
    } catch (error) {
      console.error('获取栏目详情失败:', error)
      ElMessage.error('获取栏目详情失败')
    } finally {
      loadingColumnDetails.value = false
    }
  }
  
  // 栏目详情分页处理
  const handleColumnPageChange = async (val: number) => {
    columnCurrentPage.value = val
    await loadColumnDetails()
  }
  
  const handleColumnSizeChange = async (val: number) => {
    columnPageSize.value = val
    columnCurrentPage.value = 1
    await loadColumnDetails()
  }
  
  const handleColumnDialogClose = () => {
    columnDialogVisible.value = false
    columnDetailData.value = []
    currentTaskIdForColumns.value = ''
  }
  
  // 栏目列表分页处理
  const handleColumnTablePageChange = async (val: number) => {
    columnTableCurrentPage.value = val
    await loadWebsiteColumnData(websiteId.value)
  }
  
  const handleColumnTableSizeChange = async (val: number) => {
    columnTablePageSize.value = val
    columnTableCurrentPage.value = 1
    await loadWebsiteColumnData(websiteId.value)
  }
  </script>
  
  <template>
    <PageMain>
      <div class="flex justify-between" v-auth="['temporarytask.query']">
        <div class="w-auto flex justify-between">
          <div class="flex items-center mr-6">
            <div class="mr-6 font-600">归属单位</div>
            <el-tree-select
              style="width: 200px;"
              v-model="unit_name"
              placeholder="请选择归属单位"
              filterable
              :props="{value: 'label'}"
              :data="unit_name_list"
              :render-after-expand="false"
              clearable
            >
            </el-tree-select>
          </div>
  
          <div class="flex items-center mr-6">
            <div class="mr-6 font-600">网站名称</div>
            <el-input v-model="network_name" clearable
              placeholder="请输入网站名称" style="width: 200px;"></el-input>
          </div>
  
          <div class="flex items-center mr-6">
            <div class="mr-6 font-600">开始时间</div>
            <el-date-picker
              v-model="start_time"
              type="datetime"
              placeholder="选择开始时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disableFutureDate"
              style="width: 220px;" />
          </div>
  
          <div class="flex items-center">
            <div class="mr-6 font-600">任务状态</div>
            <el-select clearable v-model="task_status"
              placeholder="请选择任务状态" style="width: 200px;">
              <el-option v-for="option in statusOptions"
                :key="option.value" :label="option.label"
                :value="option.value"></el-option>
            </el-select>
          </div>
  
          <div class="ml-6 flex items-center">
            <el-button color="#00706B"
              @click="handleQuery">查询</el-button>
          </div>
        </div>
      </div>
  
      <!-- 任务列表 -->
      <div class="mt-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-bold mb-4">任务列表</h2>
          <el-button color="#00706B" @click="handleAdd" v-auth="['temporarytask.create']">新建任务</el-button>
        </div>
        <el-table :data="tableData" border size="small" class="table-height">
          <el-table-column align="center" type="index" label="序号"
            width="70"></el-table-column>
          <el-table-column align="center" prop="belonging_unit"
            label="归属单位" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="network_name"
            label="网站名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="network_ip"
            label="域名/IP" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.network_ip || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="network_type"
            label="网络类型">
            <template #default="scope">
              {{ scope.row.network_type || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="created_at"
            label="任务开始时间"></el-table-column>
          <el-table-column align="center" label="检查栏目数量" width="120">
            <template #default="scope">
              <el-button type="primary" link
                @click="handleViewColumns(scope.row)">
                {{ scope.row.column_number || 0 }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="check_level"
            label="检查层级"
            width="100">
            <template #default="scope">
              <el-tag
                type="primary">{{ scope.row.check_level || '三级' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" label="任务状态">
            <template #default="scope">
              <el-tag v-if="scope.row.status === '巡检中'"
                type="warning">{{ scope.row.status }}</el-tag>
              <el-tag v-else-if="scope.row.status === '已完成'"
                type="success">{{ scope.row.status }}</el-tag>
              <el-tag v-else-if="scope.row.status === '任务异常'"
                type="danger">{{ scope.row.status }}</el-tag>
              <el-tag v-else>{{ scope.row.status || '-' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" label="检查结果" width="200">
            <template #default="scope">
              <div class="flex justify-center flex-wrap">
                <el-button size="small" color="#00706B"
                  @click="handleViewReportInNewWindow(scope.row.id)"
                  :disabled="scope.row.status !== '已完成'" v-auth="['temporarytask.viewreport']">查看报告</el-button>
                <el-button size="small" type="danger"
                  @click="handleDelete(scope.$index, scope.row)"
                  :disabled="scope.row.status === '巡检中'" v-auth="['temporarytask.delete']">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
  
      <!-- 翻页组件 -->
      <div class="flex justify-end mt-4">
        <el-pagination background
          layout="sizes, total, prev, pager, next"
          :page-sizes="[20, 30, 40, 50, 100]" :total="total"
          :page-count="pageCount" :page-size="pageSize"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"></el-pagination>
      </div>
  
      <!-- 报告预览对话框 -->
      <el-dialog v-model="reportDialogVisible" fullscreen center
        align-center destroy-on-close
        @closed="isRenderReportSuccess = false">
        <div :class="isRenderReportSuccess ? '' : 'h-auto'"
          ref="filePreview"
          v-loading.fullscreen.lock="!isRenderReportSuccess"
          element-loading-text="正在加载报告预览"></div>
        <template v-if="isRenderReportSuccess" #title>
          <div class="flex font-600">报告预览</div>
        </template>
        <template v-if="isRenderReportSuccess" #footer>
          <el-button @click="handleReportCancel">取消</el-button>
          <el-button color="#00706B"
            @click="handleDownloadDocWithPicker">下载</el-button>
        </template>
      </el-dialog>
  
      <!-- 新建任务对话框 -->
      <el-dialog v-model="createTaskDialogVisible" fullscreen
        destroy-on-close title="新建任务">
        <div class="flex justify-between mb-6">
          <div class="w-auto flex justify-between">
            <div class="flex items-center mr-6">
              <div class="mr-6 font-600">归属单位</div>
              <el-tree-select
                style="width: 200px;"
                v-model="selectedUnitName"
                placeholder="请选择归属单位"
                filterable
                :props="{value: 'label'}"
                :data="unit_name_list"
                :render-after-expand="false"
                clearable
              >
              </el-tree-select>
            </div>
  
            <div class="flex items-center mr-6">
              <div class="mr-6 font-600">网站名称</div>
              <el-select v-model="selectedNetworkName"
                placeholder="请选择网站名称"
                style="width: 200px;">
                <template #empty>
                  <el-empty description="请先选择归属单位" :image-size="80" />
                </template>
                <el-option v-for="(item, index) in networkNameList"
                  :key="index" :label="item" :value="item"></el-option>
              </el-select>
            </div>
  
            <div class="flex items-center mr-6">
              <div class="mr-6 font-600">网络类型</div>
              <el-select v-model="selectedNetworkType"
                placeholder="请选择网络类型"
                style="width: 200px;">
                <el-option v-for="option in networkTypeOptions"
                  :key="option.value" :label="option.label"
                  :value="option.value"></el-option>
              </el-select>
            </div>
  
            <div class="flex items-center">
              <div class="mr-6 font-600">域名/IP</div>
              <el-input v-model="networkIp" disabled placeholder="自动获取"
                style="width: 200px;"></el-input>
            </div>
          </div>
  
          <div>
             <el-button color="#00706B"
              @click="handleConfirmWebsiteSelection">确认</el-button>
            <el-button @click="handleResetCreateTask">重置</el-button>
           
          </div>
        </div>
  
        <!-- 栏目列表 -->
        <div v-loading="isLoadingColumns">
          <div class="mt-6">
            <h2 class="text-lg font-bold mb-4">栏目列表</h2>
            <el-table :data="columnTableData" border size="small"
              height="960" max-height="960"
              @selection-change="handleColumnSelectionChange">
              <el-table-column type="selection" width="55"
                align="center"></el-table-column>
              <el-table-column align="center" prop="column_type"
                label="栏目类型"></el-table-column>
              <el-table-column align="center" prop="first_column"
                label="一级栏目"></el-table-column>
              <el-table-column align="center" prop="second_column"
                label="二级栏目"></el-table-column>
              <el-table-column align="center" prop="third_column"
                label="三级栏目"></el-table-column>
              <el-table-column align="center" prop="ip_address"
                label="域名/IP"></el-table-column>
              <el-table-column align="center"
                prop="timeliness_rule_type"
                label="及时性要求（周期）">
                <template #default="scope">
                  <span>{{ scope.row?.timeliness_rule_type ? `每${timelineName(scope.row.timeliness_rule_type)}` : '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="timeliness_rule"
                label="及时性要求（次数）">
                <template #default="scope">
                  <span>{{ scope.row?.timeliness_rule_count ? `${scope.row.timeliness_rule_count}次` : '-' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
            <!-- 栏目列表分页 -->
            <div class="flex justify-end mt-4">
              <el-pagination background
                layout="sizes, total, prev, pager, next"
                :page-sizes="[20, 30, 40, 50, 100]" 
                :total="columnTableTotal"
                :page-count="columnTablePageCount" 
                :page-size="columnTablePageSize"
                @current-change="handleColumnTablePageChange"
                @size-change="handleColumnTableSizeChange"></el-pagination>
            </div>
          <!-- 底部操作栏 -->
          <div class="mt-6 flex items-center justify-center">
            <div class="mr-6 flex items-center" v-if="checkLevel">
              <span class="mr-2">检查层级:</span>
              <el-tag type="primary">{{ checkLevel }}</el-tag>
            </div>
            <el-button color="#00706B" @click="handleExecute"
              :disabled="!multipleSelection.length">执行</el-button>
          </div>
        </div>
      </el-dialog>
  
      <!-- 日期选择弹窗 -->
      <el-dialog v-model="dateRangeDialogVisible" title="选择检查时间"
        width="400px" center>
        <div class="flex justify-center items-center mb-4">
          <el-date-picker v-model="dateRange" type="daterange"
            range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" :disabled-date="disableFutureDate"
            style="width: 320px;" />
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button color="#00706B"
              @click="handleConfirmDateRange">确认</el-button>
            <el-button @click="handleCancelDateRange">取消</el-button>
            
          </span>
        </template>
      </el-dialog>
  
      <!-- 栏目详情对话框 -->
      <el-dialog v-model="columnDialogVisible" title="栏目详情"
        width="800px"
        destroy-on-close @closed="handleColumnDialogClose">
        <div v-loading="loadingColumnDetails">
          <el-table :data="columnDetailData" border size="small"
            height="400" max-height="400" style="width: 100%;">
            <el-table-column align="center" type="index" label="序号"
              width="70"></el-table-column>
            <el-table-column align="center" prop="column_type"
              label="栏目类型"></el-table-column>
            <el-table-column align="center" prop="first_column"
              label="一级栏目"></el-table-column>
            <el-table-column align="center" prop="second_column"
              label="二级栏目"></el-table-column>
            <el-table-column align="center" prop="third_column"
              label="三级栏目"></el-table-column>
            <el-table-column align="center" prop="ip_address"
              label="域名/IP"></el-table-column>
            <el-table-column align="center" prop="timeliness_rule_type"
              label="及时性要求（周期）">
              <template #default="scope">
                <span>{{ scope.row?.timeliness_rule_type ? `每${timelineName(scope.row.timeliness_rule_type)}` : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="timeliness_rule_count"
              label="及时性要求（次数）">
              <template #default="scope">
                <span>{{ scope.row?.timeliness_rule_count ? `${scope.row.timeliness_rule_count}次` : '-' }}</span>
              </template>
            </el-table-column>
          </el-table>
  
          <!-- 栏目详情分页 -->
          <div class="flex justify-end mt-4">
            <el-pagination background
              layout="sizes, total, prev, pager, next"
              :page-sizes="[20, 30, 40, 50, 100]" :total="columnTotal"
              :page-count="columnPageCount" :page-size="columnPageSize"
              @current-change="handleColumnPageChange"
              @size-change="handleColumnSizeChange"></el-pagination>
          </div>
        </div>
  
        <template #footer>
          <span class="dialog-footer">
            <el-button
              @click="columnDialogVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
    </PageMain>
  </template>
  
  <style lang="scss">
  .docx-wrapper {
    section {
      width: auto !important;

      table {
        width: 100% !important;
        table-layout: fixed !important;
        border: 1px solid #000;
      }

      tr {
        border: 1px solid #000;
      }

      td {
        border: 1px solid #000;
      }
    }
  }
  </style>
  
<route lang="yaml">
meta:
  title: 执行管理
</route>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import websiteManageApi from '@/api/modules/websiteManagement'
const { queryWebsiteEffectRule, executeWebsiteEffectRule } = websiteManageApi

const router = useRouter()
const tableData = ref([])
// 网页数据总条目数
const total = ref(100);
// 当前页码
const currentPage = ref(1);
// 总页码数量
const pageCount = ref(0)
// 每页显示的数据条目数
const pageSize = ref(20)

onMounted(async () => {
  await getWebsiteData(currentPage.value, pageSize.value)
})

const getWebsiteData = async (page_index: number, page_size: number) => {
  const res = await queryWebsiteEffectRule({ page_index, page_size })
  tableData.value = res?.data?.effecs
  total.value = res?.data?.total_num
  pageCount.value = res?.data?.page_num
}

const handlePageChange = async (val: number) => {
  currentPage.value = val;
  console.log(`当前页: ${val}`);
  await getWebsiteData(currentPage.value, pageSize.value)
};

const handleSizeChange = async (val: number) => {
  pageSize.value = val;
  console.log(`每页条数: ${val}`);
  await getWebsiteData(currentPage.value, pageSize.value)
};

async function handleExecute(index: number, row: any) {
  const res: any = await executeWebsiteEffectRule(row.id)
  console.log('执行有效性规则', res)
  if (res.code === 200) {
    ElMessage.success(res.data || '执行成功')
  } else {
    ElMessage.error(res.data || '执行失败')
  }
}

const handleTime = (index: number, row: any) => {
  router.push({
    name: 'websiteDetail',
    query: {
      websiteId: row.id,
      networkName: row.network_name,
      networkType: row.network_type
    }
  })
}

const handleEffective = (index: number, row: any) => {
  router.push({
    name: 'effective',
    query: {
      websiteId: row.id,
    }
  })
}
</script>

<template>
  <div>
    <PageMain>
      <div class="my-4">
        <el-table :data="tableData" border size="small" height="960" max-height="960">
          <el-table-column align="center" type="index" label="序号" fixed="left" width="50"></el-table-column>
          <el-table-column align="center" prop="belonging_unit" label="归属单位"></el-table-column>
          <el-table-column align="center" prop="network_name" label="网站名称" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="network_ip" label="域名/IP" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" prop="network_type" label="网络类型"></el-table-column>
          <el-table-column align="center" label="操作" fixed="right" width="200">
            <template #default="scope">
              <div class="flex justify-center flex-wrap">
                <el-button size="small" type="primary" @click="handleTime(scope.$index, scope.row)">及时性</el-button>
                <el-button size="small" type="warning" @click="handleEffective(scope.$index, scope.row)">有效性</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="flex justify-end mt-4">
          <el-pagination
            background
            layout="sizes, total, prev, pager, next"
            :page-sizes="[20, 30, 40, 50, 100]"
            :total="total"
            :page-count="pageCount"
            :page-size="pageSize"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          ></el-pagination>
        </div>
      </div>
    </PageMain>
  </div>
</template>
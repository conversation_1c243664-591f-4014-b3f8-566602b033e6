import type { RouteRecordRaw } from 'vue-router'
import { $t } from '@/locales'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/temporarytask',
  component: Layout,
  redirect: '/temporarytask/tasklist',
  name: 'temporaryTask',
  meta: {
    title: $t('route.temporarytask.tasklist'),
    icon: 'ep:tickets',
    breadcrumb: false,
    defaultOpened: true,
    auth: ['temporarytask']
  },
  children: [
    {
      path: 'tasklist',
      name: 'taskList',
      component: () => import('@/views/temporary_task/temporary_task_management.vue'),
      meta: {
        title: $t('route.temporarytask.tasklist'),
        menu: false,
        activeMenu: '/temporarytask',
      },
    },
  ],
}

export default routes

import path from 'path-browserify'

export function resolveRoutePath(basePath?: string, routePath?: string) {
  return basePath ? path.resolve(basePath, routePath ?? '') : routePath ?? ''
}

function isObject(value: any) {
  return typeof value === 'object' && !Array.isArray(value)
}
// 比较两个对象，并提取出不同的部分
export function getTwoObjectDiff(originalObj: Record<string, any>, diffObj: Record<string, any>) {
  if (!isObject(originalObj) || !isObject(diffObj)) {
    return diffObj
  }
  const diff: Record<string, any> = {}
  for (const key in diffObj) {
    const originalValue = originalObj[key]
    const diffValue = diffObj[key]
    if (JSON.stringify(originalValue) !== JSON.stringify(diffValue)) {
      if (isObject(originalValue) && isObject(diffValue)) {
        const nestedDiff = getTwoObjectDiff(originalValue, diffValue)
        if (Object.keys(nestedDiff).length > 0) {
          diff[key] = nestedDiff
        }
      }
      else {
        diff[key] = diffValue
      }
    }
  }
  return diff
}

// 从响应头中提取文件名
export function extractFilenameFromResponse(response: any, defaultFilename: string) {
  const contentDisposition = response.headers['content-disposition']
  const filenameMatch = contentDisposition.match(/filename=([^;]*)/)
  return filenameMatch ? decodeURIComponent(filenameMatch[1]) : defaultFilename
}

/**
 * 检测操作系统类型
 */
function detectOS(): 'macOS' | 'Windows' | 'Linux' | 'Other' {
  const userAgent = navigator.userAgent.toLowerCase()
  if (userAgent.includes('mac os x') || userAgent.includes('macintosh')) {
    return 'macOS'
  } else if (userAgent.includes('windows')) {
    return 'Windows'
  } else if (userAgent.includes('linux')) {
    return 'Linux'
  }
  return 'Other'
}

/**
 * 检测浏览器类型（改进版）
 */
function detectBrowser(): 'Safari' | 'Chrome' | 'Firefox' | 'Edge' | 'Other' {
  const userAgent = navigator.userAgent.toLowerCase()
  
  // 更精确的Firefox检测
  if (userAgent.includes('firefox')) {
    return 'Firefox'
  }
  
  // 更精确的Safari检测（必须包含safari但不包含chrome）
  if (userAgent.includes('safari') && !userAgent.includes('chrome') && !userAgent.includes('chromium')) {
    return 'Safari'
  }
  
  // Edge检测（新版Edge基于Chromium）
  if (userAgent.includes('edg/') || userAgent.includes('edge/')) {
    return 'Edge'
  }
  
  // Chrome检测（包括基于Chromium的浏览器，但排除Edge）
  if ((userAgent.includes('chrome') || userAgent.includes('chromium')) && !userAgent.includes('edg')) {
    return 'Chrome'
  }
  
  return 'Other'
}

/**
 * 检测 File System Access API 是否真正可用（改进版）
 */
function isFileSystemAccessSupported(): boolean {
  const browser = detectBrowser()
  const os = detectOS()
  
  // Firefox 和 Safari 已知不支持
  if (browser === 'Firefox' || browser === 'Safari') {
    return false
  }
  
  // 检查基本API是否存在
  if (!('showSaveFilePicker' in window && typeof window.showSaveFilePicker === 'function')) {
    return false
  }
  
  // 检查是否在安全上下文中
  if (!window.isSecureContext) {
    return false
  }
  
  return true
}

/**
 * 保存文件到指定路径（跨平台兼容，优化版）
 * @param blob 文件二进制内容
 * @param fileName 文件名
 * @param mimeType MIME类型，默认'application/octet-stream'
 * @returns Promise<boolean | 'default'> 返回保存状态：
 *   - true: 成功使用自定义路径保存
 *   - 'default': 成功下载到默认路径
 *   - false: 用户取消操作
 */
export async function saveFileWithPicker(blob: Blob, fileName: string, mimeType = 'application/octet-stream'): Promise<boolean | 'default'> {
  // 去除文件名中可能存在的引号
  const cleanFileName = fileName.replace(/^['"]|['"]$/g, '') || fileName
  
  const os = detectOS()
  const browser = detectBrowser()
  
  // 检查是否支持 File System Access API
  if (isFileSystemAccessSupported()) {
    try {
      // @ts-ignore
      const fileHandle = await window.showSaveFilePicker({
        suggestedName: cleanFileName,
        types: [
          {
            description: '文件',
            accept: { [mimeType]: [`.${cleanFileName.split('.').pop()}`] }
          }
        ]
      })
      
      // 用户选择了保存位置，开始显示保存提示
      let saveMessage: any = null
      
      try {
        // 动态导入ElMessage以避免依赖问题
        const { ElMessage } = await import('element-plus')
        saveMessage = ElMessage({
          message: '正在保存文件，请稍候...',
          type: 'info',
          duration: 0,
          showClose: false
        })
      } catch (e) {
        // 如果element-plus不可用，继续执行保存但不显示提示
        console.warn('无法加载ElMessage，跳过保存提示')
      }
      
      // 创建可写流进行优化写入
      const writable = await fileHandle.createWritable()
      
      try {
        // 对于大文件，使用流式写入提高性能
        if (blob.size > 10 * 1024 * 1024) { // 大于10MB的文件
          // 分块写入，每块1MB
          const chunkSize = 1024 * 1024
          let offset = 0
          
          while (offset < blob.size) {
            const chunk = blob.slice(offset, Math.min(offset + chunkSize, blob.size))
            await writable.write(chunk)
            offset += chunkSize
          }
        } else {
          // 小文件直接写入
          await writable.write(blob)
        }
        
        await writable.close()
        
        // 关闭保存提示并显示成功消息
        if (saveMessage) {
          saveMessage.close()
        }
        
        // 显示成功提示
        try {
          const { ElMessage } = await import('element-plus')
          ElMessage.success('文件保存成功')
        } catch (e) {
          console.warn('无法加载ElMessage，跳过成功提示')
        }
        
        return true // 自定义路径保存成功
        
      } catch (writeError) {
        // 关闭保存提示
        if (saveMessage) {
          saveMessage.close()
        }
        throw writeError
      }
      
    } catch (e: any) {
      if (e.name === 'AbortError') {
        // 用户取消保存，返回false但不抛出错误
        return false
      }
      // 其他错误，记录并回退到默认下载
      console.warn('File System Access API 失败，回退到默认下载:', e)
    }
  }
  
  // 回退到传统下载方式
  try {
    const blobObj = blob instanceof Blob ? blob : new Blob([blob], { type: mimeType })
    const downloadLink = document.createElement('a')
    let url: any = window.URL
    url = url.createObjectURL(blobObj)
    downloadLink.href = url
    downloadLink.download = cleanFileName
    
    // 针对不同平台提供用户体验提示
    try {
      const { ElMessage } = await import('element-plus')
      
      let message = ''
      if (browser === 'Firefox') {
        message = 'Firefox浏览器暂不支持自定义保存路径，文件已下载到默认下载文件夹'
      } else if (browser === 'Safari') {
        message = 'Safari浏览器暂不支持自定义保存路径，文件已下载到默认下载文件夹'
      } else {
        message = '当前环境不支持自定义保存路径，文件已下载到默认下载文件夹'
      }
      
      ElMessage({
        message: message,
        type: 'info',
        duration: 4000,
        showClose: true
      })
    } catch (e) {
      console.warn('无法加载ElMessage，跳过下载提示')
    }
    
    document.body.appendChild(downloadLink)
    downloadLink.click()
    document.body.removeChild(downloadLink)
    window.URL.revokeObjectURL(url)
    
    return 'default' // 默认下载成功
  } catch (error) {
    console.error('文件下载失败:', error)
    
    // 显示错误提示
    try {
      const { ElMessage } = await import('element-plus')
      ElMessage.error('文件下载失败，请稍后重试')
    } catch (e) {
      console.warn('无法加载ElMessage，跳过错误提示')
    }
    
    return false // 下载失败
  }
}
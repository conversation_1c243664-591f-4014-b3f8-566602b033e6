import api from '../index'

export default {
  // 新增角色
  addRole: (data: {
    datalist: Array<{
      name: string
    }>
  }) => api.post('/role', data),

  // 查询角色列表
  getRoleList: () => api.get('/role'),

  // 修改角色
  updateRole: (data: {
    id: string,
    name: string
  }) => api.put('/role', data),

  // 删除角色
  deleteRole: (data: {
    ids: string[]
  }) => api.delete('/role', { data }),

  // 查询角色权限
  getRolePermissions: (roleId: string) => api.get(`/rolePermission/${roleId}`),

  // 查询角色权限codename
  getRolePermissionCodenames: (roleId: string) => api.get(`/simplePermission/${roleId}`),

  // 修改角色权限
  updateRolePermissions: (data: {
    role_id: string,
    operation: string[]
  }) => api.put('/rolePermission', data),
}

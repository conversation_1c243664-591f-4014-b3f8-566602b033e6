import api from '../index'

export default {
  // 查询所有归属单位
  getUnitList: (params?: {
    page_flag?: string,
    page_index?: number,
    page_size?: number
  }) => api.get('/unit', { params }),

  // 根据分配的网站查询
  getNetworkUnit: (params?: {
    page_flag?: string,
    page_index?: number,
    page_size?: number
  }) => api.get('/networkUnit', { params }),

  // 新增单位
  addUnit: (data: {
    unit_info: Array<{
      organization: string,
      unit_name: string
    }>
  }) => api.post('/unit', data),

  // 修改单位
  updateUnit: (data: {
    id: string,
    organization: string,
    unit_name: string
  }) => api.put('/unit', data),

  // 删除单位
  deleteUnit: (data: {
    ids: string[]
  }) => api.delete('/unit', { data }),

} 
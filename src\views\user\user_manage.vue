<script setup lang="ts">
import useUserStore from '@/store/modules/user'
import useBusinessStore from '@/store/modules/business'
import { ElMessage, ElMessageBox } from 'element-plus'
import importOrExportApi from '@/api/modules/importOrExport'
import roleManageApi from '@/api/modules/roleManage'
import { extractFilenameFromResponse, saveFileWithPicker } from '@/utils'
const userStore = useUserStore()
const businessStore = useBusinessStore()
const { downloadUserTemplate, importUserExcel, exportUserExcel } = importOrExportApi

// 表格数据
const tableData = ref([])

// 分页相关变量
const total = ref(0)
const currentPage = ref(1)
const pageCount = ref(0)
const pageSize = ref(20)

// 新增：角色列表数据
const roleList: Ref<any[]> = ref([])
const roleLoading = ref(false)

// 选中的行数据
const selectedRows = ref<any[]>([])

// 获取角色列表
const getRoleList = async () => {
  try {
    roleLoading.value = true
    const res: any = await roleManageApi.getRoleList()

    if (res?.code === 200 && res?.res_code === '000000') {
      roleList.value = res.data?.page_list || []
    } else {
      console.error('获取角色列表失败:', res?.message)
      ElMessage.error('获取角色列表失败，请稍后重试')
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败，请稍后重试')
  } finally {
    roleLoading.value = false
  }
}

// 获取表格数据
const getUserList = async () => {
  const res = await userStore.getUserList({
    page_index: currentPage.value,
    page_size: pageSize.value
  })

  if (res) {
    tableData.value = res.data || []
    total.value = res.total_num || 0
    pageCount.value = res.page_num || 0
  }
}

// 处理页码变化
const handlePageChange = async (val: number) => {
  currentPage.value = val
  await getUserList()
}

// 处理每页数量变化
const handleSizeChange = async (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  await getUserList()
}

// 弹窗显示控制
const dialogVisible = ref(false)
// 导入弹窗显示控制
const importDialogVisible = ref(false)
// 权限配置弹窗显示控制
const configDialogVisible = ref(false)
// 是否为编辑模式
const isEdit = ref(false)
// 多选框选中的行 - 保留变量以避免破坏性，但不再使用
const multipleSelection: Ref<any[]> = ref([])

// 表单数据
const form = reactive({
  id: '',
  name: '',
  role: '',
  real_name: '',
  belonging_unit_id: '',
  phone: '',
  email: '',
  // id_card: ''
})

// 导入文件相关
const uploadFile = ref(null)
const fileList = ref([])

// 当前选中的用户
const currentUser: Ref<any> = ref(null)

// 穿梭框数据
const allNetworks: Ref<any[]> = ref([])
const selectedNetworkKeys: Ref<any[]> = ref([])

// 加载状态
const loading = ref(false)

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入用户名称', trigger: 'blur' },
    { min: 3, message: '用户名称不能少于3个字符', trigger: 'blur' },
  ],
  role: [{ required: true, message: '请选择用户角色', trigger: 'change' }],
  real_name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  belonging_unit_id: [{ required: true, message: '请选择单位', trigger: 'change' }],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback(new Error('请输入手机号码'))
        } else if (!/^1[3-9]\d{9}$/.test(value)) {
          callback(new Error('请输入有效的11位手机号码'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!value) {
          callback(new Error('请输入邮箱'))
        } else if (!/^[\w.-]+@[\w.-]+\.\w+$/.test(value)) {
          callback(new Error('请输入有效的邮箱地址'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  // id_card: [
  //   { required: true, message: '请输入身份证号', trigger: 'blur' },
  //   {
  //     validator: (rule: any, value: string, callback: any) => {
  //       if (!value) {
  //         callback(new Error('请输入身份证号'))
  //       } else if (/^\d{3}\*{6}\d{3}$/.test(value)) {
  //         // 脱敏格式（3位数字+6个星号+3位数字）不进行校验，直接通过
  //         callback()
  //       } else if (!/^\d{17}[\dXx]$/.test(value)) {
  //         callback(new Error('请输入有效的18位身份证号码'))
  //       } else {
  //         callback()
  //       }
  //     },
  //     trigger: 'blur'
  //   }
  // ]
})

// 编辑模式下移除密码验证规则
const getFormRules = computed(() => {
  if (isEdit.value) {
    const editRules = { ...rules } as Partial<typeof rules>
    // delete editRules.password
    return editRules
  }
  return rules
})

const formRef = ref()

// 表格选择变化 - 保留函数以避免破坏性，但标记为废弃
const tableRef = ref()

// @deprecated 该函数已废弃，保留以避免破坏性
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 打开新增弹窗
const handleAdd = () => {
  isEdit.value = false
  dialogVisible.value = true
  form.id = ''
  form.name = ''
  form.role = ''
  form.real_name = ''
  form.belonging_unit_id = ''
  form.phone = ''
  form.email = ''
  // form.id_card = ''
}

// 打开编辑弹窗 - 修改为接收行数据参数
const handleEdit = (row: any) => {
  isEdit.value = true
  dialogVisible.value = true

  // 直接使用传入的行数据，无需检查选中状态
  form.id = row.id
  form.name = row.name
  form.role = row.role_id
  form.real_name = row.real_name
  form.belonging_unit_id = row.belonging_unit_id
  // 根据belonging_unit找到对应的单位数据
  // const selectedUnit = businessStore.unitNameOriginalList.find(
  //   (item) => item.unit_name === row.belonging_unit
  // )

  // if (selectedUnit) {
  //   form.unit_name = selectedUnit.unit_name
  //   form.belonging_unit_id = selectedUnit.id
  // } else {
  //   form.unit_name = row.belonging_unit || ''
  //   form.belonging_unit_id = row.belonging_unit_id || ''
  // }

  form.phone = row.phone
  form.email = row.email
  // form.id_card = row.id_card
}

// 打开导入弹窗
const handleImport = () => {
  importDialogVisible.value = true
  fileList.value = []
}

// 下载用户信息导入模板
const downloadTemplate = async () => {
  try {
    const res = await downloadUserTemplate()
    const filename = extractFilenameFromResponse(res, '用户信息导入模板.xlsx')

    // 调用优化后的saveFileWithPicker
    const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    if (saveResult === true) {
      // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
    } else if (saveResult === 'default') {
      // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
    } else {
      // 用户取消操作
      ElMessage.info('模板下载已取消')
    }
  } catch(error) {
    console.error('下载模板失败:', error)
    ElMessage.error('模板下载失败，请稍后重试')
  }
}

// 文件上传前的验证
const beforeUpload = (file: any) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件(格式为xlsx或xls)')
    return false
  }
  return true
}

// 文件变化处理，确保uploadFile与fileList同步
const handleFileChange = (file: any, uploadFileList: any) => {
  if (file.status === 'ready') {
    uploadFile.value = file.raw
    fileList.value = uploadFileList
  }
}

// 文件移除处理
const handleRemove = () => {
  uploadFile.value = null
  fileList.value = []
}

// 确认导入前再次检查文件状态
const confirmImport = async () => {
  if (!uploadFile.value || fileList.value.length === 0) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  try {
    const formData = new FormData()
    formData.append('file', uploadFile.value)

    const res: any = await importUserExcel(formData)
    if (res?.code === 200 && res.res_code === '000000') {
      ElMessage.success(res.message || '用户信息导入成功')
      importDialogVisible.value = false
      // 刷新数据
      await getUserList()
    } else {
      ElMessage.warning(res?.message || res.data || '导入失败，请检查文件格式或查看文件是否有未填项')
    }
  } catch (error) {
    console.error('导入失败:', error)
    // ElMessage.error('导入失败，请稍后重试')
  }
}

// 取消导入
const cancelImport = () => {
  importDialogVisible.value = false
  uploadFile.value = null
  fileList.value = []
}

// 提交新增/编辑
const handleSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (!form.belonging_unit_id) {
        ElMessage.warning('请选择有效的单位')
        return false
      }

      if (isEdit.value) {
        // 编辑用户
        const updateData = {
          id: form.id,
          name: form.name,
          role_id: form.role,
          real_name: form.real_name,
          belonging_unit_id: form.belonging_unit_id,
          phone: form.phone,
          email: form.email,
          // id_card: form.id_card
        }

        // 修改：从表格数据中找到对应的用户信息，替代之前的 multipleSelection[0]
        const selectedUser: any = tableData.value.find((user: any) => user.id === form.id)

        // 修改：由于现在使用角色ID，需要找到对应的角色名称来判断是否为系统管理员
        const selectedRole = roleList.value.find((role: any) => role.id === form.role)
        const isSystemAdmin = selectedRole?.name === '系统管理员'
        const wasSystemAdmin = selectedUser?.role &&
          roleList.value.find((role: any) => role.id === selectedUser.role)?.name === '系统管理员'

        if (isSystemAdmin && !wasSystemAdmin) {
          // 显示警告弹窗
          ElMessageBox.alert(
            '如果将其他用户角色调整为系统管理员，确认移交后下次登录时将自动调整为普通用户！',
            '警告',
            {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
            .then(async () => {
              // 用户点击确认，执行更新操作
              const res = await userStore.updateUser(updateData)
              if (res) {
                dialogVisible.value = false
                // 清空表单
                resetForm()
                // 刷新数据
                await getUserList()
              }
            })
            .catch(() => {
              // 用户点击取消，不执行任何操作
              ElMessage.info('已取消修改')
            })
        } else {
          // 常规更新逻辑
          const res = await userStore.updateUser(updateData)
          if (res) {
            dialogVisible.value = false
            // 清空表单
            resetForm()
            // 刷新数据
            await getUserList()
          }
        }
      } else {
        // 新增用户
        const params = {
          name: form.name,
          role_id: form.role,
          real_name: form.real_name,
          belonging_unit_id: form.belonging_unit_id,
          phone: form.phone,
          email: form.email,
        }
        const res = await userStore.addUser(params)
        if (res) {
          dialogVisible.value = false
          // 清空表单
          resetForm()
          // 刷新数据
          await getUserList()
        }
      }
    } else {
      ElMessage.warning('请按要求填写表单')
      return false
    }
  })
}

// 重置表单 - 抽取公共方法减少代码重复
const resetForm = () => {
  form.id = ''
  form.name = ''
  form.role = ''
  form.real_name = ''
  form.belonging_unit_id = ''
  form.phone = ''
  form.email = ''
  // form.id_card = ''
}

// 取消新增/编辑也使用resetForm
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 批量编辑用户
const handleBatchEdit = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要编辑的用户')
    return
  }

  // 执行编辑操作
  handleEdit(selectedRows.value[0])
}

// 批量删除用户
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的用户')
    return
  }

  ElMessageBox.alert(
    `是否确认删除以上用户信息?`,
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      // 用户点击确认，执行批量删除操作
      try {
        const ids = selectedRows.value.map((row: any) => row.id).join(',')
        const res = await userStore.deleteUser(ids)
        if (res) {
          selectedRows.value = []
          // 刷新数据
          await getUserList()
        }
      } catch (error) {
        console.error('删除用户失败:', error)
        ElMessage.error('删除用户失败，请稍后重试')
      }
    })
    .catch(() => {
      // 用户点击取消，不执行任何操作
      ElMessage.info('已取消删除')
    })
}

// 权限配置
const handleConfig = async (row: any) => {
  currentUser.value = row
  configDialogVisible.value = true

  loading.value = true
  try {
    // 获取未分配的网站列表
    const unassignedNetworks = await userStore.getUserNetworks(row.id, '0')
    // 获取已分配的网站列表
    const assignedNetworks = await userStore.getUserNetworks(row.id, '1')

    // 设置穿梭框数据
    allNetworks.value = [...unassignedNetworks, ...assignedNetworks].map(item => ({
      key: item.id,
      label: `${item.network_name}(${item.network_type})`,
      disabled: false
    }))

    // 设置已选择的网站
    selectedNetworkKeys.value = assignedNetworks.map((item: any) => item.id)

    loading.value = false
  } catch (error) {
    loading.value = false
    ElMessage.error('获取网站列表失败')
    configDialogVisible.value = false
  }
}

// 保存权限配置
const saveConfig = async () => {
  if (!currentUser.value) return

  loading.value = true
  try {
    // 获取当前已分配的网站
    const assignedNetworks = await userStore.getUserNetworks(currentUser.value.id, '1')
    const oldKeys = assignedNetworks.map((item: any) => item.id)

    // 计算需要新增和删除的网站
    const toAdd = selectedNetworkKeys.value.filter((key: any) => !oldKeys.includes(key))
    const toRemove = oldKeys.filter((key: any) => !selectedNetworkKeys.value.includes(key))

    let addSuccess = true
    let removeSuccess = true

    // 添加新分配的网站
    if (toAdd.length > 0) {
        addSuccess = await userStore.assignNetworksToUser(currentUser.value.id, {
        network_info_ids: toAdd
      })
    }

    // 删除取消分配的网站
    if (toRemove.length > 0) {
      removeSuccess = await userStore.removeNetworksFromUser(currentUser.value.id, {
        network_info_ids: toRemove
      })
    }

    if (addSuccess && removeSuccess) {
      configDialogVisible.value = false
    }

    loading.value = false
  } catch (error) {
    loading.value = false
    ElMessage.error('保存权限配置失败')
  }
}

// 取消权限配置
const cancelConfig = () => {
  configDialogVisible.value = false
  currentUser.value = null
  selectedNetworkKeys.value = []
}

// 下载文件工具方法
const downloadFile = (data: any, fileName: string) => {
  const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = fileName
  link.click()
  URL.revokeObjectURL(link.href)
}

// 单位选择变化处理函数
const handleUnitChange = (unitName: string) => {
  const selectedUnit: any = businessStore.unitNameOriginalList.find((item: any) => item.unit_name === unitName)
  if (selectedUnit) {
    form.belonging_unit_id = selectedUnit.id
  } else {
    form.belonging_unit_id = ''
  }
}

// 导出用户Excel
const handleExport = async () => {
  try {
    const res = await exportUserExcel()
    const filename = extractFilenameFromResponse(res, '用户信息.xlsx')

    // 调用优化后的saveFileWithPicker
    const saveResult = await saveFileWithPicker(res.data, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    if (saveResult === true) {
      // 自定义路径保存成功，saveFileWithPicker内部已显示成功提示
    } else if (saveResult === 'default') {
      // 默认路径下载成功，saveFileWithPicker内部已显示相关提示
    } else {
      // 用户取消操作
      ElMessage.info('导出已取消')
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

onMounted(() => {
  getUserList()
  businessStore.getUnitNameList()
  // 新增：获取角色列表
  getRoleList()
})
</script>

<template>
  <PageMain>
    <div class="flex justify-between mb-4">
      <h2 class="text-lg font-bold">用户信息列表</h2>
      <div class="w-auto flex justify-between items-center">
        <el-button color="#00706B" @click="handleImport" v-auth="['roleandusermanage.usermanage.import']">导入</el-button>
        <el-button color="#00706B" @click="handleExport" v-auth="['roleandusermanage.usermanage.export']">导出</el-button>
        <el-button color="#00706B" @click="handleAdd" v-auth="['roleandusermanage.usermanage.add']">新增</el-button>
        <el-button color="#00706B" @click="handleBatchEdit" v-auth="['roleandusermanage.usermanage.edit']" :disabled="selectedRows?.length !== 1">编辑</el-button>
        <el-button color="#00706B" @click="handleBatchDelete" v-auth="['roleandusermanage.usermanage.delete']" :disabled="!selectedRows?.length">删除</el-button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="my-4">
      <el-table
        ref="tableRef"
        :data="tableData"
        border
        size="small"
        class="table-height-query"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column align="center" prop="name" label="用户名"></el-table-column>
        <el-table-column align="center" prop="real_name" label="姓名"></el-table-column>
        <!-- <el-table-column align="center" prop="id_card" label="身份证号"></el-table-column> -->
        <el-table-column align="center" prop="belonging_unit" label="单位"></el-table-column>
        <el-table-column align="center" prop="phone" label="电话"></el-table-column>
        <el-table-column align="center" prop="email" label="邮箱"></el-table-column>
        <el-table-column align="center" prop="role" label="用户角色">
          <!-- <template #default="scope">
            {{ getRoleName(scope.row.role) }}
          </template> -->
        </el-table-column>
        <!-- <el-table-column align="center" label="操作" fixed="right" width="120">
          <template #default="scope">
            <div class="flex justify-center flex-wrap">
              <el-button size="small" color="#00706B" @click="handleEdit(scope.row)" v-auth="['roleandusermanage.usermanage.edit']">编辑</el-button>
             <el-button size="small" color="#00706B" @click="handleConfig(scope.row)">权限配置</el-button>
            </div>
          </template>
        </el-table-column> -->
      </el-table>
    </div>

    <!-- 翻页组件 -->
    <div class="flex justify-end">
      <el-pagination
        background
        layout="sizes, total, prev, pager, next"
        :page-size="pageSize"
        :page-sizes="[20, 30, 40, 50, 100]"
        :total="total"
        :page-count="pageCount"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      ></el-pagination>
    </div>

    <!-- 新增/编辑用户弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      width="500"
      align-center
      center
      destroy-on-close>
      <el-form
        :model="form"
        :rules="getFormRules"
        ref="formRef"
        label-position="right"
        label-width="100px">
        <el-form-item label="用户名" prop="name">
          <el-input v-model="form.name"
            placeholder="请输入用户名称"></el-input>
        </el-form-item>
        <el-form-item label="真实姓名" prop="real_name">
          <el-input v-model="form.real_name"
            placeholder="请输入真实姓名"></el-input>
        </el-form-item>
        <!-- <el-form-item label="身份证号" prop="id_card">
          <el-input v-model="form.id_card"
            placeholder="请输入身份证号"></el-input>
        </el-form-item> -->
        <el-form-item label="单位名称" prop="belonging_unit_id">
           <el-tree-select
              v-model="form.belonging_unit_id"
              placeholder="请选择归属单位"
              filterable
               :data="businessStore.unitNameOriginalList"
               :render-after-expand="false"
              >
            </el-tree-select>
          <!-- <el-select
            v-model="form.unit_name"
            placeholder="请选择单位"
            @change="handleUnitChange"
            filterable>
            <el-option
              v-for="item in businessStore.unitNameOriginalList"
              :key="item.id"
              :label="item.unit_name"
              :value="item.unit_name">
            </el-option>
          </el-select> -->
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="form.phone"
            placeholder="请输入手机号码"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email"
            placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="用户角色" prop="role">
          <!-- 修改：使用动态角色列表 -->
          <el-select
            v-model="form.role"
            placeholder="请选择用户角色"
            :loading="roleLoading"
            :disabled="roleLoading">
            <el-option
              v-for="role in roleList"
              :key="role.id"
              :label="role.name"
              :value="role.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #header="{ titleId }">
          <div :id="titleId" class="flex font-600 mb-6">{{isEdit ? '编辑用户信息' : '新增用户'}}</div>
        </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="handleSubmit">确认</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入用户弹窗 -->
    <el-dialog v-model="importDialogVisible" width="500" center align-center destroy-on-close>
      <div class="mb-4 flex flex-col items-center">
        <div class="flex items-center mb-4">
          <span class="mr-2">导入模板:</span>
          <el-button type="text" @click="downloadTemplate">点击下载用户信息导入模板</el-button>
        </div>

        <div class="flex items-center">
          <span class="mr-2">导入文件:</span>
          <div>
            <!-- 添加一个提示条件性显示 -->
            <div v-if="fileList.length > 0" class="mb-2 text-orange-500 text-xs">
              如需更换文件，请先点击文件右侧的"×"删除当前文件
            </div>
            <el-upload
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :limit="1"
              :file-list="fileList"
              :on-change="handleFileChange"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              accept=".xlsx,.xls"
            >
              <!-- 只有当fileList为空时才允许上传 -->
              <el-button color="#00706B" :disabled="fileList.length > 0">文件上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传xlsx/xls文件
                </div>
              </template>
            </el-upload>
          </div>
        </div>
      </div>
      <template #header="{ titleId }">
        <div :id="titleId" class="flex font-600 mb-6">导入</div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="confirmImport">确认</el-button>
          <el-button @click="cancelImport">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限配置弹窗 -->
    <el-dialog v-model="configDialogVisible" width="700" center align-center destroy-on-close>
      <div class="flex justify-center" v-loading="loading">
        <el-transfer
          v-model="selectedNetworkKeys"
          :data="allNetworks"
          :titles="['所有网站名称', '已分配的网站']"
        />
      </div>
      <template #header="{ titleId }">
        <div :id="titleId" class="flex font-600 mb-6">权限配置</div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button color="#00706B" @click="saveConfig" :loading="loading">保存</el-button>
          <el-button @click="cancelConfig">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </PageMain>
</template>

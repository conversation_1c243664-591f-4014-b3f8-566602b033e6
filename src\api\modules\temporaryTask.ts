import api from '../index'

export default {
  // 查询网站名称
  queryNetworkNames: (unit_name: string = '') => api.get(`/network_info/network_name?unit_name=${unit_name}`),

  // 根据归属单位、网站名称、网络类型查询网站
  queryNetworkDetail: (params: {
    network_name?: string,
    unit_name?: string,
    network_type?: string
  }) => api.get('/network_info/detial', {
    params: {
      ...params
    }
  }),

  // 查询巡检任务
  queryInspectionTasks: (params: {
    page_index?: number,
    page_size?: number,
    unit_name?: string,
    start_time?: string,
    network_name?: string,
    status?: string
  }) => api.get('/asyncTask', {
    params: {
      ...params
    }
  }),

  // 新建巡检任务
  createInspectionTask: (data: {
    website_id: string,
    column_ids: string[],
    start_date?: string,
    end_date?: string
  }) => api.post('/asyncTask', data),

  // 查询巡检栏目
  queryInspectionColumns: (taskId: string, params: {
    page_index?: number,
    page_size?: number
  }) => api.get(`/asyncTaskColumn/${taskId}`, {
    params: {
      ...params
    }
  }),

  // 删除巡检任务
  deleteInspectionTask: (data: { ids: string[] }) => {
    const url = `/asyncTask`
    return api.delete(url, { data });
  },

  // 查看、下载巡检报告
  downloadInspectionReport: (taskId: string) => api.get(`/taskReport/${taskId}`, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/octet-stream'
    }
  })
}
